import React, { useState, useEffect } from 'react'
import { Layout, <PERSON>u, Button, Dropdown, Avatar, Space, Typography, Badge, Tooltip } from 'antd'
import { 
  DashboardOutlined,
  RobotOutlined,
  DatabaseOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  <PERSON>goutOutlined,
  <PERSON>Outlined,
  SearchOutlined,
  MoonOutlined,
  SunOutlined,
  FullscreenOutlined,
  HomeOutlined
} from '@ant-design/icons'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAuthStore } from '../stores/authStore'

const { Header, Sider, Content } = Layout
const { Text } = Typography

interface MainLayoutProps {
  children: React.ReactNode
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false)
  const [darkMode, setDarkMode] = useState(false)
  const [currentTime, setCurrentTime] = useState(new Date())
  const navigate = useNavigate()
  const location = useLocation()
  const { user, logout } = useAuthStore()

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  useEffect(() => {
    if (darkMode) {
      document.documentElement.setAttribute('data-theme', 'dark')
    } else {
      document.documentElement.removeAttribute('data-theme')
    }
  }, [darkMode])

  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '智能大屏',
      onClick: () => navigate('/dashboard')
    },
    {
      key: '/ai-config',
      icon: <RobotOutlined />,
      label: 'AI配置',
      onClick: () => navigate('/ai-config')
    },
    {
      key: '/data',
      icon: <DatabaseOutlined />,
      label: '数据管理',
      onClick: () => navigate('/data')
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
      onClick: () => navigate('/settings')
    }
  ]

  const handleLogout = () => {
    logout()
    navigate('/login')
  }

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料'
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '账户设置'
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '安全退出',
      onClick: handleLogout
    }
  ]

  const getPageTitle = (pathname: string) => {
    const titles: Record<string, string> = {
      '/dashboard': '智能数据大屏',
      '/ai-config': 'AI模型配置',
      '/data': '数据管理中心',
      '/settings': '系统设置'
    }
    return titles[pathname] || '评语灵感君'
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 现代化侧边栏 */}
      <div className={`fixed left-0 top-0 h-full z-50 transition-all duration-300 ${
        collapsed ? 'w-20' : 'w-72'
      }`}>
        <div className="h-full bg-white/80 backdrop-blur-xl border-r border-white/50 shadow-2xl">
          {/* Logo区域 */}
          <div className="nav-brand-container">
            {!collapsed ? (
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <HomeOutlined className="text-white text-lg" />
                </div>
                <div>
                  <div className="brand-name-title text-lg">评语灵感君</div>
                  <div className="text-xs text-gray-500">智能管理系统</div>
                </div>
              </div>
            ) : (
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                <HomeOutlined className="text-white text-lg" />
              </div>
            )}
          </div>

          {/* 导航菜单 */}
          <div className="p-4">
            <div className="space-y-2">
              {menuItems.map((item) => {
                const isActive = location.pathname === item.key
                return (
                  <Tooltip title={collapsed ? item.label : ''} placement="right" key={item.key}>
                    <div
                      onClick={item.onClick}
                      className={`flex items-center space-x-4 px-4 py-3 rounded-2xl cursor-pointer transition-all duration-300 group ${
                        isActive
                          ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg transform scale-105'
                          : 'hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 hover:shadow-md hover:transform hover:scale-102'
                      }`}
                    >
                      <div className={`text-xl ${isActive ? 'text-white' : 'text-gray-600 group-hover:text-blue-500'}`}>
                        {item.icon}
                      </div>
                      {!collapsed && (
                        <span className={`font-medium ${isActive ? 'text-white' : 'text-gray-700'}`}>
                          {item.label}
                        </span>
                      )}
                      {!collapsed && isActive && (
                        <div className="ml-auto w-2 h-2 bg-white rounded-full"></div>
                      )}
                    </div>
                  </Tooltip>
                )
              })}
            </div>
          </div>

          {/* 底部用户信息 */}
          {!collapsed && (
            <div className="absolute bottom-4 left-4 right-4">
              <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-4 border border-gray-100">
                <div className="flex items-center space-x-3">
                  <Avatar 
                    size={40} 
                    className="bg-gradient-to-r from-blue-500 to-purple-600" 
                    icon={<UserOutlined />} 
                  />
                  <div className="flex-1">
                    <div className="font-semibold text-gray-800 text-sm">
                      {user?.name || '系统管理员'}
                    </div>
                    <div className="text-xs text-gray-500">
                      在线 • {currentTime.toLocaleTimeString()}
                    </div>
                  </div>
                  <Badge dot color="#52c41a" />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 主内容区域 */}
      <div className={`transition-all duration-300 ${collapsed ? 'ml-20' : 'ml-72'}`}>
        {/* 现代化头部 - 匹配侧边栏高度 */}
        <div className="h-20 bg-white/80 backdrop-blur-xl border-b border-white/50 shadow-lg sticky top-0 z-40">
          <div className="h-full flex items-center justify-between px-6">
            <div className="flex items-center space-x-6">
              <Button
                type="text"
                icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                onClick={() => setCollapsed(!collapsed)}
                className="!w-12 !h-12 !rounded-xl hover:!bg-blue-50 hover:!text-blue-600 transition-all duration-300"
              />
              <div>
                <div className="nav-brand-title" style={{ color: darkMode ? 'var(--text-primary)' : '#1e293b' }}>
                  {getPageTitle(location.pathname)}
                </div>
                <div className="text-sm" style={{ color: darkMode ? 'var(--text-secondary)' : '#64748b' }}>
                  {currentTime.toLocaleDateString('zh-CN', { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric',
                    weekday: 'long' 
                  })}
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* 搜索按钮 */}
              <Tooltip title="全局搜索">
                <Button
                  type="text"
                  icon={<SearchOutlined />}
                  className="!w-12 !h-12 !rounded-xl hover:!bg-blue-50 hover:!text-blue-600"
                />
              </Tooltip>
              
              {/* 通知按钮 */}
              <Tooltip title="消息通知">
                <Badge count={3} size="small">
                  <Button
                    type="text"
                    icon={<BellOutlined />}
                    className="!w-12 !h-12 !rounded-xl hover:!bg-blue-50 hover:!text-blue-600"
                  />
                </Badge>
              </Tooltip>

              {/* 全屏按钮 */}
              <Tooltip title="全屏显示">
                <Button
                  type="text"
                  icon={<FullscreenOutlined />}
                  className="!w-12 !h-12 !rounded-xl hover:!bg-blue-50 hover:!text-blue-600"
                  onClick={() => {
                    if (document.fullscreenElement) {
                      document.exitFullscreen()
                    } else {
                      document.documentElement.requestFullscreen()
                    }
                  }}
                />
              </Tooltip>

              {/* 主题切换 */}
              <Tooltip title={darkMode ? '切换到亮色模式' : '切换到暗色模式'}>
                <Button
                  type="text"
                  icon={darkMode ? <SunOutlined /> : <MoonOutlined />}
                  onClick={() => setDarkMode(!darkMode)}
                  className="!w-12 !h-12 !rounded-xl hover:!bg-blue-50 hover:!text-blue-600"
                />
              </Tooltip>
              
              {/* 用户菜单 */}
              <Dropdown
                menu={{ items: userMenuItems }}
                placement="bottomRight"
                trigger={['click']}
              >
                <div className="flex items-center space-x-3 px-4 py-2 rounded-2xl hover:bg-blue-50 cursor-pointer transition-all duration-300">
                  <Avatar 
                    size={36} 
                    className="bg-gradient-to-r from-blue-500 to-purple-600" 
                    icon={<UserOutlined />} 
                  />
                  <div className="hidden md:block">
                    <div className="text-sm font-semibold text-gray-800">
                      {user?.name || '系统管理员'}
                    </div>
                    <div className="text-xs text-gray-500">超级管理员</div>
                  </div>
                </div>
              </Dropdown>
            </div>
          </div>
        </div>
        
        {/* 内容区域 */}
        <div className="min-h-[calc(100vh-5rem)]">
          {children}
        </div>
      </div>
    </div>
  )
}

export default MainLayout