import React, { useState, useEffect, useMemo, Suspense, lazy } from 'react'
import { Row, Col, Card, Statistic, Typography, Space, Button, Skeleton, Alert } from 'antd'
import { 
  UserOutlined, 
  MessageOutlined, 
  RobotOutlined, 
  TrophyOutlined,
  <PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON><PERSON>utlined,
  <PERSON><PERSON>pOutlined,
  <PERSON>DownOutlined,
  LineChartOutlined,
  <PERSON><PERSON>hartOutlined,
  EyeOutlined,
  RocketOutlined,
  Badge,
  Progress,
  Tooltip
} from '@ant-design/icons'

const { Title, Text } = Typography

// 懒加载性能诊断工具
const PerformanceOptimizer = lazy(() => import('../components/PerformanceOptimizer'))

const Dashboard: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [showPerformanceTools, setShowPerformanceTools] = useState(false)
  const [performanceAlert, setPerformanceAlert] = useState(false)
  const [frameRate, setFrameRate] = useState(60)
  const [memoryUsage, setMemoryUsage] = useState(0)

  // 🔥 修复：减少更新频率，从1秒改为30秒
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 30000) // 30秒更新一次就够了
    
    return () => {
      clearInterval(timer)
    }
  }, [])

  // 🔥 修复：实时性能监控
  useEffect(() => {
    let frameCount = 0
    let lastTime = performance.now()
    
    const monitorPerformance = () => {
      frameCount++
      const currentTime = performance.now()
      
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime))
        setFrameRate(fps)
        
        // 检查内存使用
        const memory = (performance as any).memory
        if (memory) {
          const usedMB = Math.round(memory.usedJSHeapSize / 1048576)
          setMemoryUsage(usedMB)
        }
        
        // 性能警告
        if (fps < 30) {
          setPerformanceAlert(true)
        } else {
          setPerformanceAlert(false)
        }
        
        frameCount = 0
        lastTime = currentTime
      }
      
      requestAnimationFrame(monitorPerformance)
    }
    
    requestAnimationFrame(monitorPerformance)
  }, [])

  // 现代化的统计数据
  const stats = [
    {
      title: '总用户数',
      value: 1234,
      icon: <UserOutlined />,
      trend: { value: 12.5, type: 'up' },
      suffix: '人',
      color: 'from-blue-500 to-cyan-500',
      bgColor: 'bg-gradient-to-br from-blue-50 to-cyan-50',
      description: '活跃教师用户'
    },
    {
      title: '今日生成评语',
      value: 110,
      icon: <MessageOutlined />,
      trend: { value: 8.3, type: 'up' },
      suffix: '条',
      color: 'from-green-500 to-emerald-500',
      bgColor: 'bg-gradient-to-br from-green-50 to-emerald-50',
      description: '智能评语生成'
    },
    {
      title: 'AI调用次数',
      value: 2456,
      icon: <RobotOutlined />,
      trend: { value: 15.7, type: 'up' },
      suffix: '次',
      color: 'from-purple-500 to-pink-500',
      bgColor: 'bg-gradient-to-br from-purple-50 to-pink-50',
      description: 'AI服务调用'
    },
    {
      title: '系统评分',
      value: 987,
      icon: <TrophyOutlined />,
      trend: { value: 2.1, type: 'up' },
      suffix: '分',
      color: 'from-orange-500 to-red-500',
      bgColor: 'bg-gradient-to-br from-orange-50 to-red-50',
      description: '用户满意度'
    }
  ]

  const recentActivities = [
    { 
      user: '张老师', 
      action: '生成了5条学生评语', 
      time: '2分钟前', 
      avatar: '张',
      type: 'generate',
      icon: <MessageOutlined />,
      color: 'bg-blue-500'
    },
    { 
      user: '李老师', 
      action: '导入了新的学生数据', 
      time: '15分钟前', 
      avatar: '李',
      type: 'import',
      icon: <ThunderboltOutlined />,
      color: 'bg-green-500'
    },
    { 
      user: '王老师', 
      action: '修改了AI配置参数', 
      time: '1小时前', 
      avatar: '王',
      type: 'config',
      icon: <RobotOutlined />,
      color: 'bg-purple-500'
    },
    { 
      user: '赵老师', 
      action: '导出了月度报告', 
      time: '2小时前', 
      avatar: '赵',
      type: 'export',
      icon: <BarChartOutlined />,
      color: 'bg-orange-500'
    },
    { 
      user: '孙老师', 
      action: '批量生成了30条评语', 
      time: '3小时前', 
      avatar: '孙',
      type: 'batch',
      icon: <RocketOutlined />,
      color: 'bg-pink-500'
    }
  ]

  const systemMetrics = [
    { 
      name: 'CPU使用率', 
      value: 65, 
      status: 'normal',
      icon: <LineChartOutlined />,
      color: 'text-blue-500',
      bgColor: 'from-blue-500 to-blue-600'
    },
    { 
      name: '内存使用率', 
      value: 78, 
      status: 'warning',
      icon: <PieChartOutlined />,
      color: 'text-orange-500',
      bgColor: 'from-orange-500 to-orange-600'
    },
    { 
      name: '存储空间', 
      value: 45, 
      status: 'normal',
      icon: <BarChartOutlined />,
      color: 'text-green-500',
      bgColor: 'from-green-500 to-green-600'
    },
    { 
      name: 'API响应', 
      value: 95, 
      status: 'excellent',
      icon: <ThunderboltOutlined />,
      color: 'text-purple-500',
      bgColor: 'from-purple-500 to-purple-600'
    }
  ]

  const quickActions = [
    { name: '生成评语', icon: <MessageOutlined />, color: 'from-blue-500 to-blue-600' },
    { name: 'AI配置', icon: <RobotOutlined />, color: 'from-purple-500 to-purple-600' },
    { name: '数据管理', icon: <BarChartOutlined />, color: 'from-green-500 to-green-600' },
    { name: '系统设置', icon: <TrophyOutlined />, color: 'from-orange-500 to-orange-600' }
  ]

  const getProgressStatus = (value: number, status: string) => {
    if (status === 'excellent') return 'success'
    if (status === 'warning') return 'exception'
    return 'normal'
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-6">
      {/* 性能警告 */}
      {performanceAlert && (
        <Alert
          message={`⚠️ 性能问题检测 - FPS: ${frameRate}, 内存: ${memoryUsage}MB`}
          description={
            <div>
              系统运行缓慢，建议开启性能优化工具进行诊断。
              <Button 
                type="link" 
                size="small"
                onClick={() => setShowPerformanceTools(true)}
                className="ml-2"
              >
                立即优化
              </Button>
            </div>
          }
          type="warning"
          closable
          className="mb-4"
        />
      )}

      {/* 页面头部 */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <Title level={1} className="!mb-2 !text-gray-800 flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                <BarChartOutlined className="text-2xl text-white" />
              </div>
              智能数据大屏
            </Title>
            <Text className="text-gray-600 text-lg">
              实时监控系统运行状态和使用情况
            </Text>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <div className="text-2xl font-bold text-gray-800 mb-1">
                {currentTime.toLocaleTimeString()}
              </div>
              <div className="text-gray-500">
                {currentTime.toLocaleDateString('zh-CN', { 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric',
                  weekday: 'long' 
                })}
              </div>
            </div>
            <div className="flex flex-col items-center">
              <div className="text-sm text-gray-500 mb-1">性能监控</div>
              <div className="flex items-center gap-2">
                <span className={`text-xs px-2 py-1 rounded ${frameRate >= 60 ? 'bg-green-100 text-green-600' : frameRate >= 30 ? 'bg-yellow-100 text-yellow-600' : 'bg-red-100 text-red-600'}`}>
                  {frameRate}fps
                </span>
                <Button
                  type={showPerformanceTools ? "primary" : "default"}
                  size="small"
                  icon={<ThunderboltOutlined />}
                  onClick={() => setShowPerformanceTools(!showPerformanceTools)}
                >
                  {showPerformanceTools ? '关闭' : '诊断'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 性能优化工具 */}
      {showPerformanceTools && (
        <div className="mb-6">
          <Suspense fallback={
            <Card loading title="加载性能诊断工具..." className="mb-4">
              <Skeleton active paragraph={{ rows: 6 }} />
            </Card>
          }>
            <PerformanceOptimizer />
          </Suspense>
        </div>
      )}

      {/* 核心统计卡片 */}
      <Row gutter={[24, 24]} className="mb-8">
        {stats.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <div className={`${stat.bgColor} rounded-2xl p-6 border border-white/50 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 relative overflow-hidden group cursor-pointer`}>
              {/* 背景装饰 */}
              <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
                <div className={`w-full h-full bg-gradient-to-br ${stat.color} rounded-full transform translate-x-6 -translate-y-6 group-hover:scale-110 transition-transform duration-500`}></div>
              </div>
              
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-4">
                  <div className={`w-12 h-12 bg-gradient-to-r ${stat.color} rounded-xl flex items-center justify-center text-white shadow-lg`}>
                    {stat.icon}
                  </div>
                  <div className="flex items-center space-x-1">
                    {stat.trend.type === 'up' ? (
                      <ArrowUpOutlined className="text-green-500" />
                    ) : (
                      <ArrowDownOutlined className="text-red-500" />
                    )}
                    <span className={`text-sm font-semibold ${stat.trend.type === 'up' ? 'text-green-500' : 'text-red-500'}`}>
                      {stat.trend.value}%
                    </span>
                  </div>
                </div>
                
                <div className="mb-2">
                  <div className="text-3xl font-bold text-gray-800 mb-1">
                    {stat.value.toLocaleString()}{stat.suffix}
                  </div>
                  <div className="text-gray-600 font-medium">
                    {stat.title}
                  </div>
                  <div className="text-sm text-gray-500 mt-1">
                    {stat.description}
                  </div>
                </div>
              </div>
            </div>
          </Col>
        ))}
      </Row>

      <Row gutter={[24, 24]}>
        {/* 系统性能指标 */}
        <Col xs={24} lg={16}>
          <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 border border-white/50 shadow-xl">
            <div className="flex items-center justify-between mb-6">
              <Title level={3} className="!mb-0 flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <LineChartOutlined className="text-white text-sm" />
                </div>
                系统性能监控
              </Title>
              <Badge status="processing" text="实时监控" />
            </div>
            
            <Row gutter={[16, 16]}>
              {systemMetrics.map((metric, index) => (
                <Col xs={24} sm={12} key={index}>
                  <div className="bg-gradient-to-r from-white to-gray-50 rounded-xl p-4 border border-gray-100 hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className={`w-10 h-10 bg-gradient-to-r ${metric.bgColor} rounded-lg flex items-center justify-center text-white shadow-md`}>
                          {metric.icon}
                        </div>
                        <div>
                          <div className="font-semibold text-gray-800">{metric.name}</div>
                          <div className="text-sm text-gray-500">当前状态</div>
                        </div>
                      </div>
                      <div className={`text-2xl font-bold ${metric.color}`}>
                        {metric.value}%
                      </div>
                    </div>
                    <Progress 
                      percent={metric.value} 
                      status={getProgressStatus(metric.value, metric.status)}
                      showInfo={false}
                      strokeWidth={8}
                      trailColor="#f1f5f9"
                      strokeLinecap="round"
                    />
                  </div>
                </Col>
              ))}
            </Row>
          </div>
        </Col>

        {/* 快速操作 */}
        <Col xs={24} lg={8}>
          <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 border border-white/50 shadow-xl mb-6">
            <Title level={3} className="!mb-4 flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                <RocketOutlined className="text-white text-sm" />
              </div>
              快速操作
            </Title>
            
            <div className="grid grid-cols-2 gap-3">
              {quickActions.map((action, index) => (
                <Tooltip title={action.name} key={index}>
                  <Button 
                    type="text" 
                    className="h-16 border border-gray-200 hover:border-blue-300 rounded-xl flex flex-col items-center justify-center gap-2 hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
                  >
                    <div className={`w-8 h-8 bg-gradient-to-r ${action.color} rounded-lg flex items-center justify-center text-white`}>
                      {action.icon}
                    </div>
                    <span className="text-xs font-medium text-gray-600">{action.name}</span>
                  </Button>
                </Tooltip>
              ))}
            </div>
          </div>

          {/* 实时活动流 */}
          <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 border border-white/50 shadow-xl">
            <div className="flex items-center justify-between mb-4">
              <Title level={3} className="!mb-0 flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
                  <EyeOutlined className="text-white text-sm" />
                </div>
                实时动态
              </Title>
              <Badge count={recentActivities.length} showZero color="#52c41a" />
            </div>
            
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {recentActivities.map((activity, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-gradient-to-r from-gray-50 to-white rounded-xl border border-gray-100 hover:shadow-md transition-all duration-300">
                  <div className={`w-10 h-10 ${activity.color} rounded-full flex items-center justify-center text-white shadow-md flex-shrink-0`}>
                    {activity.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-semibold text-gray-800 text-sm mb-1">
                      {activity.user}
                    </div>
                    <div className="text-gray-600 text-sm mb-1 break-words">
                      {activity.action}
                    </div>
                    <div className="text-xs text-gray-400">
                      {activity.time}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Col>
      </Row>
    </div>
  )
}

export default Dashboard