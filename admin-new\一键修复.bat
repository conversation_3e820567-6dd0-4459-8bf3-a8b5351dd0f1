@echo off
echo.
echo ========================================
echo    一键修复所有依赖问题
echo ========================================
echo.

echo 🧹 清理旧文件...
if exist "node_modules" rmdir /s /q "node_modules"
if exist "package-lock.json" del "package-lock.json"
if exist "yarn.lock" del "yarn.lock"

echo 🔄 清理npm缓存...
call npm cache clean --force

echo 📦 重新安装依赖...
echo    尝试方法1: npm install --legacy-peer-deps
call npm install --legacy-peer-deps

if errorlevel 1 (
    echo    方法1失败，尝试方法2: yarn
    call npm install -g yarn
    call yarn install
    
    if errorlevel 1 (
        echo    方法2失败，尝试方法3: pnpm
        call npm install -g pnpm
        call pnpm install
    )
)

echo.
echo ✅ 依赖安装完成！
echo.
echo 🚀 启动开发服务器...
echo.

call npm run dev

echo.
echo ========================================
echo    按任意键退出...
echo ========================================
pause >nul