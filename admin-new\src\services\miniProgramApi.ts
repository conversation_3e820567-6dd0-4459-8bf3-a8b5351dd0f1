/**
 * 小程序数据连通API服务
 * 负责与小程序后端进行数据交互
 */

// API基础配置
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'https://your-miniprogram-api.com/api'
const API_TIMEOUT = 10000

// 请求头配置
const getHeaders = () => ({
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${localStorage.getItem('admin_token') || ''}`,
  'X-Client-Type': 'admin-panel'
})

// 通用请求函数
const request = async (endpoint: string, options: RequestInit = {}): Promise<any> => {
  const url = `${API_BASE_URL}${endpoint}`
  
  const config: RequestInit = {
    timeout: API_TIMEOUT,
    headers: getHeaders(),
    ...options
  }

  try {
    const response = await fetch(url, config)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const data = await response.json()
    return data
  } catch (error) {
    console.error(`API请求失败 [${endpoint}]:`, error)
    throw error
  }
}

// 用户管理API
export const userApi = {
  // 获取用户列表
  getUsers: async (params?: { page?: number; limit?: number; search?: string }) => {
    const query = new URLSearchParams(params as any).toString()
    return request(`/users?${query}`)
  },
  
  // 获取用户详情
  getUserDetail: async (userId: string) => {
    return request(`/users/${userId}`)
  },
  
  // 更新用户使用限额
  updateUserLimit: async (userId: string, limit: number) => {
    return request(`/users/${userId}/limit`, {
      method: 'PUT',
      body: JSON.stringify({ limit })
    })
  },
  
  // 重置用户使用次数
  resetUserUsage: async (userId: string) => {
    return request(`/users/${userId}/reset-usage`, {
      method: 'POST'
    })
  }
}

// 评语管理API
export const commentApi = {
  // 获取评语列表
  getComments: async (params?: { page?: number; limit?: number; userId?: string }) => {
    const query = new URLSearchParams(params as any).toString()
    return request(`/comments?${query}`)
  },
  
  // 获取评语统计
  getCommentStats: async (timeRange?: 'day' | 'week' | 'month') => {
    return request(`/comments/stats?range=${timeRange || 'day'}`)
  }
}

// Tokens消耗API
export const tokensApi = {
  // 获取tokens消耗统计
  getTokensUsage: async (mode: 'hour' | 'day' = 'hour') => {
    return request(`/tokens/usage?mode=${mode}`)
  },
  
  // 获取tokens消耗详情
  getTokensDetail: async (startDate: string, endDate: string) => {
    return request(`/tokens/detail?start=${startDate}&end=${endDate}`)
  }
}

// AI配置API
export const aiConfigApi = {
  // 获取AI配置
  getAIConfig: async () => {
    return request('/ai/config')
  },
  
  // 更新AI配置
  updateAIConfig: async (config: {
    selectedModel: string
    apiConfigs: Record<string, any>
    aiParams: Record<string, any>
  }) => {
    return request('/ai/config', {
      method: 'PUT',
      body: JSON.stringify(config)
    })
  },
  
  // 测试AI连接
  testAIConnection: async (modelKey: string, config: any) => {
    return request('/ai/test-connection', {
      method: 'POST',
      body: JSON.stringify({ modelKey, config })
    })
  }
}

// 系统统计API
export const statsApi = {
  // 获取系统概览统计
  getOverviewStats: async () => {
    return request('/stats/overview')
  },
  
  // 获取实时活动日志
  getActivityLogs: async (limit: number = 10) => {
    return request(`/stats/activities?limit=${limit}`)
  }
}

// 数据导入导出API
export const dataApi = {
  // 导入学生数据
  importStudents: async (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    
    return request('/data/import/students', {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token') || ''}`,
        'X-Client-Type': 'admin-panel'
        // 不设置 Content-Type，让浏览器自动设置 multipart/form-data
      }
    })
  },
  
  // 导出学生数据
  exportStudents: async (format: 'csv' | 'xlsx' = 'csv') => {
    const response = await fetch(`${API_BASE_URL}/data/export/students?format=${format}`, {
      headers: getHeaders()
    })
    
    if (!response.ok) {
      throw new Error('导出失败')
    }
    
    return response.blob()
  }
}

// WebSocket连接管理（实时数据更新）
export class RealtimeConnection {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 3000

  connect(onMessage: (data: any) => void, onError?: (error: any) => void) {
    const wsUrl = API_BASE_URL.replace('http', 'ws') + '/ws'
    
    try {
      this.ws = new WebSocket(wsUrl)
      
      this.ws.onopen = () => {
        console.log('WebSocket连接已建立')
        this.reconnectAttempts = 0
        
        // 发送认证信息
        this.ws?.send(JSON.stringify({
          type: 'auth',
          token: localStorage.getItem('admin_token')
        }))
      }
      
      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          onMessage(data)
        } catch (error) {
          console.error('WebSocket消息解析失败:', error)
        }
      }
      
      this.ws.onclose = () => {
        console.log('WebSocket连接已关闭')
        this.attemptReconnect(onMessage, onError)
      }
      
      this.ws.onerror = (error) => {
        console.error('WebSocket错误:', error)
        onError?.(error)
      }
      
    } catch (error) {
      console.error('WebSocket连接失败:', error)
      onError?.(error)
    }
  }
  
  private attemptReconnect(onMessage: (data: any) => void, onError?: (error: any) => void) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connect(onMessage, onError)
      }, this.reconnectInterval)
    } else {
      console.error('WebSocket重连失败，已超过最大尝试次数')
    }
  }
  
  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }
  
  send(data: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data))
    } else {
      console.warn('WebSocket未连接，无法发送消息')
    }
  }
}

// 错误处理工具
export const handleApiError = (error: any) => {
  if (error.message?.includes('401')) {
    // 未授权，清除token并跳转到登录
    localStorage.removeItem('admin_token')
    window.location.reload()
    return '登录已过期，请重新登录'
  } else if (error.message?.includes('403')) {
    return '权限不足'
  } else if (error.message?.includes('404')) {
    return '请求的资源不存在'
  } else if (error.message?.includes('500')) {
    return '服务器内部错误'
  } else {
    return error.message || '网络错误，请稍后重试'
  }
}