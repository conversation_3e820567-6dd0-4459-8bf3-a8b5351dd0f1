/**
 * 数据报告页面
 * 展示用户的使用统计、趋势分析和智能洞察
 */
const app = getApp();

// 安全加载徽章管理器
let achievementManager = null;
try {
  const achievementModule = require('../../../utils/achievementManager');
  achievementManager = achievementModule.achievementManager;
  console.log('[报告页面] 徽章管理器加载成功');
} catch (error) {
  console.error('[报告页面] 徽章管理器加载失败:', error);
  console.log('[报告页面] 将使用内置徽章计算逻辑');
  achievementManager = null;
}

// 获取云服务实例
function getCloudService() {
  try {
    const cloudService = app.globalData.cloudService;
    if (!cloudService) {
      console.warn('云服务未初始化');
      return null;
    }
    return cloudService;
  } catch (error) {
    console.error('获取云服务失败:', error);
    return null;
  }
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 时间范围选项
    timeTabs: [
      { value: 'week', name: '近7天' },
      { value: 'month', name: '近30天' },
      { value: 'quarter', name: '近3个月' }
    ],
    currentTimeRange: 'month',

    // 核心指标概览
    overview: {
      totalComments: 0,
      timeSaved: 0,
      avgQuality: 0,
      excellentRate: 0,
      commentsGrowth: 0,
      timeGrowth: 0,
      qualityGrowth: 0,
      excellentGrowth: 0
    },

    // 效率统计
    efficiencyStats: {
      avgTime: '0',
      passRate: '0',
      totalWords: '0',
      consecutiveDays: '0'
    },

    // 质量详情
    qualityDetails: {
      professional: '0',
      personalized: '0',
      completeness: '0'
    },

    // 评语类型分布
    commentTypes: [],

    // 质量统计
    qualityStats: {
      excellent: 0,
      good: 0,
      normal: 0,
      poor: 0
    },

    // 使用频率统计
    frequencyStats: {
      avgDaily: '0条',
      peakHour: '--:--',
      activeDays: '0天',
      avgDuration: '0分钟'
    },

    // 学生覆盖情况
    studentCoverage: {
      totalStudents: 0,
      coveredStudents: 0,
      coverageRate: 0
    },

    // 每日记录统计数据
    dailyRecordsData: [],
    maxDailyRecords: 0,

    // 智能洞察
    insights: [],

    // 成就徽章
    achievements: [],

    // 页面状态
    loading: false,
    refreshing: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('报告页面加载，参数:', options);
    this.initializeData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.refreshData();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.setData({ refreshing: true });
    this.refreshData().finally(() => {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 初始化页面数据
   */
  async initializeData() {
    try {
      this.setData({ loading: true });
      
      // 分步加载，确保即使某个步骤失败也不影响整体
      
      // 1. 加载统计数据
      try {
        await this.loadStatistics();
      } catch (statsError) {
        console.error('统计数据加载失败:', statsError);
      }
      
      // 2. 加载徽章状态
      try {
        await this.loadAchievements();
      } catch (achieveError) {
        console.error('徽章数据加载失败:', achieveError);
      }
      
      // 3. 生成洞察
      try {
        this.generateInsights();
      } catch (insightsError) {
        console.error('洞察生成失败:', insightsError);
      }
      
    } catch (error) {
      console.error('初始化数据失败:', error);
      wx.showToast({
        title: '部分数据加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    try {
      await this.loadStatistics();
      await this.loadAchievements();
      this.generateInsights();
    } catch (error) {
      console.error('刷新数据失败:', error);
    }
  },

  /**
   * 加载统计数据
   */
  async loadStatistics() {
    try {
      const cloudService = getCloudService();
      
      if (cloudService) {
        // 尝试从云端获取真实数据
        const result = await this.loadCloudStatistics(cloudService);
        if (result.success) {
          this.processCloudData(result.data);
          return;
        }
      }
      
      // 使用本地数据或模拟数据
      this.loadLocalStatistics();
      
    } catch (error) {
      console.error('加载统计数据失败:', error);
      this.loadLocalStatistics();
    }
  },

  /**
   * 从云端加载统计数据
   */
  async loadCloudStatistics(cloudService) {
    try {
      const timeRange = this.data.currentTimeRange;
      const endTime = new Date();
      let startTime = new Date();
      
      // 根据时间范围设置开始时间
      if (timeRange === 'week') {
        startTime.setDate(startTime.getDate() - 7);
      } else if (timeRange === 'month') {
        startTime.setDate(startTime.getDate() - 30);
      } else {
        startTime.setDate(startTime.getDate() - 90);
      }

      // 获取评语统计
      const commentsResult = await cloudService.getCommentList({
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        pageSize: 1000
      });

      // 获取学生统计
      const studentsResult = await cloudService.getStudentList({
        pageSize: 1000
      });

      // 获取记录数据
      const recordsResult = await cloudService.getRecordList({
        pageSize: 1000
      });

      if (commentsResult.success && studentsResult.success && recordsResult.success) {
        // 处理评语数据结构
        const commentsData = commentsResult.data || {};
        const comments = commentsData.comments || commentsData || [];

        // 处理学生数据结构
        const studentsData = studentsResult.data || {};
        const students = studentsData.students || studentsData || [];

        // 处理记录数据结构
        const recordsData = recordsResult.data || {};
        const records = recordsData.records || recordsData || [];

        console.log('[报告页面] 云端数据获取成功，评语:', comments.length, '学生:', students.length, '记录:', records.length);

        return {
          success: true,
          data: {
            comments,
            students,
            records
          }
        };
      }

      return { success: false };
    } catch (error) {
      console.error('云端数据获取失败:', error);
      return { success: false };
    }
  },

  /**
   * 处理云端数据
   */
  processCloudData(data) {
    const { comments = [], students = [], records = [] } = data;

    console.log('[报告页面] 处理云端数据:', { comments: comments.length, students: students.length, records: records.length });

    // 计算核心指标
    const totalComments = comments.length;
    const avgQuality = this.calculateAverageQuality(comments);
    const timeSaved = totalComments * 3; // 假设每条评语节省3分钟
    const excellentRate = this.calculateExcellentRate(comments);

    // 计算趋势数据（相比上个周期）
    const previousPeriodData = this.getPreviousPeriodData(comments);
    const commentsGrowth = this.calculateGrowth(totalComments, previousPeriodData.comments);
    const qualityGrowth = this.calculateGrowth(avgQuality, previousPeriodData.avgQuality);

    // 计算评语类型分布
    const commentTypes = this.calculateCommentTypeDistribution(comments);
    
    // 计算质量统计
    const qualityStats = this.calculateQualityStats(comments);
    
    // 计算使用频率
    const frequencyStats = this.calculateFrequencyStats(comments);
    
    // 计算学生覆盖情况
    const studentCoverage = this.calculateStudentCoverage(comments, students);

    // 计算效率详情
    const efficiencyStats = this.calculateEfficiencyStats(comments);

    // 计算质量详情
    const qualityDetails = this.calculateQualityDetails(comments);

    // 生成每日记录统计数据
    const dailyRecordsData = this.generateDailyRecordsData(records);
    const maxDailyRecords = dailyRecordsData.length > 0 ? Math.max(...dailyRecordsData.map(item => item.count)) : 10;

    // 更新数据
    this.setData({
      overview: {
        totalComments,
        timeSaved: Math.round(timeSaved),
        avgQuality: typeof avgQuality === 'number' ? avgQuality.toFixed(1) : '0.0',
        excellentRate: Math.round(excellentRate),
        commentsGrowth: Math.round(commentsGrowth),
        timeGrowth: Math.round(commentsGrowth), // 时间节省增长与评语增长一致
        qualityGrowth: typeof qualityGrowth === 'number' ? qualityGrowth.toFixed(1) : '0.0',
        excellentGrowth: Math.round(excellentRate - (previousPeriodData.excellentRate || 0))
      },
      efficiencyStats,
      qualityDetails,
      commentTypes,
      qualityStats,
      frequencyStats,
      studentCoverage,
      dailyRecordsData,
      maxDailyRecords
    });

    // 生成智能洞察
    this.generateInsights();
    
    console.log('[报告页面] 数据处理完成');
  },

  /**
   * 加载本地统计数据
   */
  loadLocalStatistics() {
    try {
      // 从本地存储获取数据
      const savedComments = wx.getStorageSync('savedComments') || [];
      const recentComments = wx.getStorageSync('recentComments') || [];
      const students = wx.getStorageSync('students') || [];
      
      const allComments = [...savedComments, ...recentComments];
      
      if (allComments.length > 0) {
        // 使用本地真实数据
        this.processLocalData(allComments, students);
      } else {
        // 没有数据时保持初始值（全为0）
        console.log('没有本地数据，保持初始值');
      }
      
    } catch (error) {
      console.error('加载本地数据失败:', error);
    }
  },



  /**
   * 处理本地数据
   */
  processLocalData(comments, students) {
    // 筛选时间范围内的评语
    const filteredComments = this.filterCommentsByTimeRange(comments);
    
    // 计算各项统计
    const totalComments = filteredComments.length;
    const avgQuality = this.calculateAverageQuality(filteredComments);
    const timeSaved = totalComments * 0.08;
    const efficiency = Math.round((timeSaved / (totalComments * 0.2)) * 100);

    const commentTypes = this.calculateCommentTypeDistribution(filteredComments);
    const qualityStats = this.calculateQualityStats(filteredComments);
    const frequencyStats = this.calculateFrequencyStats(filteredComments);
    const studentCoverage = this.calculateStudentCoverage(filteredComments, students);

    this.setData({
      overview: {
        ...this.data.overview,
        totalComments,
        avgQuality,
        timeSaved: timeSaved.toFixed(1),
        efficiency
      },
      commentTypes,
      qualityStats,
      frequencyStats,
      studentCoverage
    });
  },

  /**
   * 根据时间范围筛选评语
   */
  filterCommentsByTimeRange(comments) {
    const timeRange = this.data.currentTimeRange;
    const now = new Date();
    let startTime = new Date();

    if (timeRange === 'week') {
      startTime.setDate(startTime.getDate() - 7);
    } else if (timeRange === 'month') {
      startTime.setDate(startTime.getDate() - 30);
    } else {
      startTime.setDate(startTime.getDate() - 90);
    }

    return comments.filter(comment => {
      const createTime = new Date(comment.createTime || comment.generateTime || Date.now());
      return createTime >= startTime;
    });
  },

  /**
   * 计算平均质量分
   */
  calculateAverageQuality(comments) {
    if (comments.length === 0) return 0;

    // 如果评语有score字段，使用score
    if (comments.some(comment => comment.score !== undefined)) {
      const totalScore = comments.reduce((sum, comment) => sum + (comment.score || 8.0), 0);
      return totalScore / comments.length;
    }

    // 否则基于内容长度计算质量分
    let totalScore = 0;
    comments.forEach(comment => {
      const length = comment.content?.length || 0;
      let score = 0;
      if (length >= 50 && length <= 100) {
        score = 9.0;
      } else if (length >= 30 && length <= 150) {
        score = 8.0;
      } else if (length >= 20) {
        score = 7.0;
      } else {
        score = 6.0;
      }
      totalScore += score;
    });

    return totalScore / comments.length;
  },

  /**
   * 计算评语类型分布
   */
  calculateCommentTypeDistribution(comments) {
    const typeCounts = {};
    const typeNames = {
      positive: '积极表现',
      learning: '学习态度',
      social: '社交能力',
      creative: '创新思维',
      discipline: '纪律表现',
      other: '其他表现'
    };

    comments.forEach(comment => {
      const type = comment.behaviorType || 'other';
      typeCounts[type] = (typeCounts[type] || 0) + 1;
    });

    const total = comments.length;
    const distribution = Object.entries(typeCounts).map(([type, count]) => ({
      type,
      name: typeNames[type] || type,
      count,
      percentage: total > 0 ? ((count / total) * 100).toFixed(1) : 0
    })).sort((a, b) => b.count - a.count);

    return distribution;
  },

  /**
   * 计算质量统计
   */
  calculateQualityStats(comments) {
    const stats = { excellent: 0, good: 0, normal: 0, poor: 0 };
    
    comments.forEach(comment => {
      const score = comment.score || 8.0;
      if (score >= 9) stats.excellent++;
      else if (score >= 7) stats.good++;
      else if (score >= 5) stats.normal++;
      else stats.poor++;
    });

    return stats;
  },

  /**
   * 计算使用频率统计
   */
  calculateFrequencyStats(comments) {
    if (comments.length === 0) {
      return {
        avgDaily: '0条',
        peakHour: '无数据',
        activeDays: '0天',
        avgDuration: '0分钟'
      };
    }

    const timeRange = this.data.currentTimeRange;
    const days = timeRange === 'week' ? 7 : timeRange === 'month' ? 30 : 90;
    
    const avgDaily = (comments.length / days).toFixed(1);
    
    // 计算活跃天数
    const activeDaysSet = new Set();
    comments.forEach(comment => {
      const date = new Date(comment.createTime || comment.generateTime || Date.now());
      activeDaysSet.add(date.toDateString());
    });

    return {
      avgDaily: `${avgDaily}条`,
      peakHour: '14:00-16:00', // 模拟数据
      activeDays: `${activeDaysSet.size}天`,
      avgDuration: '8分钟' // 模拟数据
    };
  },

  /**
   * 计算学生覆盖情况
   */
  calculateStudentCoverage(comments, students) {
    const totalStudents = students.length;
    if (totalStudents === 0) {
      return { totalStudents: 0, coveredStudents: 0, coverageRate: 0 };
    }

    const coveredStudentsSet = new Set();
    comments.forEach(comment => {
      if (comment.studentName) {
        coveredStudentsSet.add(comment.studentName);
      }
    });

    const coveredStudents = coveredStudentsSet.size;
    const coverageRate = Math.round((coveredStudents / totalStudents) * 100);

    return { totalStudents, coveredStudents, coverageRate };
  },

  /**
   * 生成智能洞察
   */
  generateInsights() {
    const insights = [];
    const { overview, qualityStats, studentCoverage } = this.data;

    // 质量提升洞察
    if (overview.qualityGrowth > 0) {
      insights.push({
        id: 1,
        type: 'tip',
        icon: '💡',
        title: '评语质量持续提升',
        description: `您的评语平均质量分比上期提升了${overview.qualityGrowth}%，继续保持！`,
        action: 'viewQualityTrend',
        actionText: '查看趋势'
      });
    }

    // 覆盖率洞察
    if (studentCoverage.coverageRate < 90) {
      const uncoveredCount = studentCoverage.totalStudents - studentCoverage.coveredStudents;
      insights.push({
        id: 2,
        type: 'warning',
        icon: '⚠️',
        title: '部分学生覆盖不足',
        description: `有${uncoveredCount}名学生本期还未生成评语，建议关注一下。`,
        action: 'viewUncoveredStudents',
        actionText: '查看详情'
      });
    }

    // 效率提升洞察
    if (overview.efficiency > 120) {
      insights.push({
        id: 3,
        type: 'success',
        icon: '🎉',
        title: '工作效率显著提升',
        description: `相比传统方式，您已节省了${overview.timeSaved}小时的评语编写时间！`,
        action: null,
        actionText: null
      });
    }

    // 质量分布洞察
    if (qualityStats.excellent > qualityStats.good + qualityStats.normal) {
      insights.push({
        id: 4,
        type: 'success',
        icon: '⭐',
        title: '评语质量优异',
        description: `您有${qualityStats.excellent}条优秀评语，占比超过一半，质量控制很好！`,
        action: null,
        actionText: null
      });
    }

    this.setData({ insights });
  },

  /**
   * 计算优秀率
   */
  calculateExcellentRate(comments) {
    if (comments.length === 0) return 0;
    const excellentCount = comments.filter(comment => (comment.score || 8.0) >= 9.0).length;
    return (excellentCount / comments.length) * 100;
  },

  /**
   * 获取上个周期数据
   */
  getPreviousPeriodData(comments) {
    // 根据当前时间范围计算上个周期的数据
    const timeRange = this.data.currentTimeRange;
    const now = new Date();
    let periodDays = 30;
    
    if (timeRange === 'week') {
      periodDays = 7;
    } else if (timeRange === 'month') {
      periodDays = 30;
    } else {
      periodDays = 90;
    }
    
    const currentPeriodStart = new Date(now.getTime() - (periodDays * 24 * 60 * 60 * 1000));
    const previousPeriodStart = new Date(currentPeriodStart.getTime() - (periodDays * 24 * 60 * 60 * 1000));
    
    const previousComments = comments.filter(comment => {
      const createTime = new Date(comment.createTime || comment.generateTime || Date.now());
      return createTime >= previousPeriodStart && createTime < currentPeriodStart;
    });
    
    return {
      comments: previousComments.length,
      avgQuality: this.calculateAverageQuality(previousComments),
      excellentRate: this.calculateExcellentRate(previousComments)
    };
  },

  /**
   * 计算增长率
   */
  calculateGrowth(current, previous) {
    if (!previous || previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  },

  /**
   * 计算评语类型分布
   */
  calculateCommentTypeDistribution(comments) {
    const typeCounts = {};
    const typeNames = {
      positive: '积极表现',
      learning: '学习态度', 
      social: '社交能力',
      creative: '创新思维',
      discipline: '纪律表现',
      other: '其他表现'
    };

    comments.forEach(comment => {
      const type = comment.behaviorType || 'other';
      typeCounts[type] = (typeCounts[type] || 0) + 1;
    });

    const total = comments.length;
    return Object.entries(typeCounts).map(([type, count]) => ({
      type,
      name: typeNames[type] || type,
      count,
      percentage: total > 0 ? ((count / total) * 100).toFixed(1) : 0
    })).sort((a, b) => b.count - a.count);
  },

  /**
   * 计算效率统计
   */
  calculateEfficiencyStats(comments) {
    if (comments.length === 0) {
      return {
        avgTime: '0分钟',
        passRate: '0%',
        totalWords: '0',
        consecutiveDays: '0天'
      };
    }

    // 计算平均用时（假设每条评语平均8分钟）
    const avgTime = '8分钟';
    
    // 计算通过率（分数>=7的比例）
    const passCount = comments.filter(comment => (comment.score || 8.0) >= 7.0).length;
    const passRate = `${Math.round((passCount / comments.length) * 100)}%`;
    
    // 计算总字数
    const totalWords = comments.reduce((sum, comment) => {
      return sum + (comment.content ? comment.content.length : 0);
    }, 0).toString();
    
    // 计算连续天数
    const activeDaysSet = new Set();
    comments.forEach(comment => {
      const date = new Date(comment.createTime || comment.generateTime || Date.now());
      activeDaysSet.add(date.toDateString());
    });
    const consecutiveDays = `${activeDaysSet.size}天`;

    return {
      avgTime,
      passRate, 
      totalWords,
      consecutiveDays
    };
  },

  /**
   * 计算质量详情
   */
  calculateQualityDetails(comments) {
    if (comments.length === 0) {
      return {
        professional: '0',
        personalized: '0', 
        completeness: '0'
      };
    }

    // 基于评语质量分数计算各项指标
    const avgQuality = this.calculateAverageQuality(comments);
    const avgScore = typeof avgQuality === 'number' ? avgQuality : parseFloat(avgQuality) || 8.0;
    
    return {
      professional: Math.min(95, Math.round(avgScore * 10 + 10)).toString(),
      personalized: Math.min(92, Math.round(avgScore * 9 + 15)).toString(),
      completeness: Math.min(88, Math.round(avgScore * 8 + 20)).toString()
    };
  },

  /**
   * 生成每日记录统计数据
   */
  generateDailyRecordsData(records) {
    const timeRange = this.data.currentTimeRange;
    let days = 7;

    if (timeRange === 'month') {
      days = 30;
    } else if (timeRange === 'quarter') {
      days = 90;
    }

    const dailyData = [];
    const now = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now.getTime() - (i * 24 * 60 * 60 * 1000));
      const dateStr = date.toDateString();

      const dayRecords = records.filter(record => {
        const recordDate = new Date(record.createTime || Date.now());
        return recordDate.toDateString() === dateStr;
      });

      dailyData.push({
        date: `${date.getMonth() + 1}/${date.getDate()}`,
        count: dayRecords.length,
        fullDate: dateStr
      });
    }

    return dailyData;
  },

  /**
   * 加载成就徽章状态
   */
  async loadAchievements() {
    try {
      console.log('[报告页面] 开始加载徽章状态...');

      // 优先使用外部徽章管理器
      if (achievementManager) {
        try {
          console.log('[报告页面] 使用外部徽章管理器...');

          // 先触发徽章检查，确保新达成的徽章被解锁
          console.log('[报告页面] 触发徽章检查...');
          await achievementManager.checkAllAchievements();

          // 然后获取最新的徽章状态
          const achievementStatus = await achievementManager.getAllAchievementStatus();
          this.setData({ achievements: achievementStatus });
          console.log('[报告页面] 外部徽章管理器加载完成:', achievementStatus.length, '个徽章');

          // 输出每个徽章的详细状态
          achievementStatus.forEach(achievement => {
            console.log(`[报告页面] ${achievement.icon} ${achievement.name}: achieved=${achievement.achieved}, isUnlocked=${achievement.isUnlocked}, current=${achievement.current}/${achievement.target}`);
          });

          return;
        } catch (managerError) {
          console.warn('[报告页面] 外部徽章管理器失败，使用内置逻辑:', managerError);
        }
      }

      // 如果外部管理器不可用，使用内置的徽章计算功能
      console.log('[报告页面] 使用内置徽章计算逻辑...');
      await this.calculateRealAchievements();

    } catch (error) {
      console.error('[报告页面] 加载徽章状态失败:', error);
      // 设置模拟徽章数据，确保页面正常显示
      await this.setMockAchievements();
    }
  },

  /**
   * 计算真实徽章数据（内置徽章管理逻辑）
   */
  async calculateRealAchievements() {
    try {
      console.log('[报告页面] 开始计算真实徽章数据...');

      // 优先使用当前页面的数据
      const { overview, qualityStats } = this.data;
      const totalComments = overview.totalComments || 0;
      const avgQuality = parseFloat(overview.avgQuality) || 0;

      // 从云数据库获取真实的记录和评语数据
      const cloudService = getCloudService();

      // 获取记录数据
      const recordResult = await cloudService.getRecordList({ pageSize: 1000 });
      const records = recordResult.success ? recordResult.data : [];

      // 获取评语数据
      const commentResult = await cloudService.getCommentList({ pageSize: 1000 });
      const comments = commentResult.success ? commentResult.data : [];

      // 获取已解锁的徽章
      const unlockedAchievements = wx.getStorageSync('unlockedAchievements') || [];

      console.log('[报告页面] 获取到的数据:', {
        records: records.length,
        comments: comments.length,
        unlockedAchievements: unlockedAchievements.length
      });

      console.log('[报告页面] 数据统计:', {
        totalComments,
        avgQuality,
        excellentCount: qualityStats.excellent || 0,
        records: records.length,
        comments: comments.length
      });
      
      const achievements = [
        {
          id: 'efficiency_master',
          name: '效率达人',
          description: '累计记录10条行为记录',
          icon: '🚀',
          target: 10,
          checkFunction: () => this.checkTotalRecords(records, 10)
        },
        {
          id: 'quality_expert',
          name: '质量专家',
          description: '连续5次评语8分以上',
          icon: '⭐',
          target: 5,
          checkFunction: () => this.checkQualityStreak(comments, 5)
        },
        {
          id: 'progress_mentor',
          name: '进步导师',
          description: '累计记录50条行为记录',
          icon: '📚',
          target: 50,
          checkFunction: () => this.checkTotalRecords(records, 50)
        },
        {
          id: 'ai_education_expert',
          name: 'AI教育专家',
          description: '累计记录200条行为记录',
          icon: '👑',
          target: 200,
          checkFunction: () => this.checkTotalRecords(records, 200)
        }
      ];

      // 计算每个徽章的状态
      const achievementStatus = achievements.map(achievement => {
        const result = achievement.checkFunction();
        const wasUnlocked = unlockedAchievements.some(a => a.id === achievement.id);
        const isAchieved = result.current >= achievement.target; // 修正达成条件判断

        console.log(`[报告页面] 徽章 ${achievement.name}:`, {
          current: result.current,
          target: achievement.target,
          isAchieved,
          wasUnlocked,
          finalUnlocked: wasUnlocked || isAchieved
        });

        return {
          ...achievement,
          current: result.current,
          progress: Math.round(result.progress),
          isUnlocked: wasUnlocked || isAchieved, // 如果达到条件也算解锁
          unlockedAt: wasUnlocked ? unlockedAchievements.find(a => a.id === achievement.id)?.unlockedAt : null
        };
      });

      // 使用新的云端徽章系统处理新解锁的徽章
      const newlyUnlocked = achievementStatus.filter(achievement =>
        achievement.isUnlocked && !unlockedAchievements.some(a => a.id === achievement.id)
      );

      if (newlyUnlocked.length > 0) {
        console.log('[报告页面] 发现新解锁徽章:', newlyUnlocked.map(a => a.name));

        try {
          // 使用云端徽章管理器处理解锁
          const AchievementManager = require('../../../utils/achievementManager');
          const achievementManager = new AchievementManager();

          // 确保初始化
          await achievementManager.init();

          // 逐个解锁新徽章
          for (const achievement of newlyUnlocked) {
            try {
              await achievementManager.unlockAchievement({
                id: achievement.id,
                name: achievement.name,
                description: achievement.description,
                icon: achievement.icon
              });
              console.log('[报告页面] 徽章解锁并同步到云端:', achievement.name);
            } catch (unlockError) {
              console.warn('[报告页面] 徽章解锁失败:', achievement.name, unlockError);
            }
          }

          // 显示解锁提示
          this.showAchievementUnlockToast(newlyUnlocked);

        } catch (error) {
          console.error('[报告页面] 云端徽章系统处理失败:', error);

          // 降级到原有的本地存储方式
          const updatedUnlocked = [
            ...unlockedAchievements,
            ...newlyUnlocked.map(achievement => ({
              id: achievement.id,
              name: achievement.name,
              unlockedAt: new Date().toISOString()
            }))
          ];
          wx.setStorageSync('unlockedAchievements', updatedUnlocked);
          console.log('[报告页面] 降级到本地存储模式');

          // 显示解锁提示
          this.showAchievementUnlockToast(newlyUnlocked);
        }
      }

      this.setData({ achievements: achievementStatus });
      console.log('[报告页面] 真实徽章数据计算完成:', achievementStatus);
      
    } catch (error) {
      console.error('[报告页面] 计算真实徽章数据失败:', error);
      await this.setMockAchievements();
    }
  },

  /**
   * 检查单日记录数
   */
  checkDailyRecords(records, target) {
    const today = new Date().toDateString();
    const todayRecords = records.filter(record => {
      const recordDate = new Date(record.createTime || Date.now()).toDateString();
      return recordDate === today;
    });
    
    const current = todayRecords.length;
    const progress = Math.min(100, (current / target) * 100);
    
    return { current, progress };
  },

  /**
   * 检查质量连续记录
   */
  checkQualityStreak(comments, target) {
    const sortedComments = comments.sort((a, b) => {
      const timeA = new Date(a.createTime || a.generateTime || 0).getTime();
      const timeB = new Date(b.createTime || b.generateTime || 0).getTime();
      return timeB - timeA;
    });

    let streak = 0;
    for (const comment of sortedComments) {
      const score = comment.score || 0;
      if (score >= 8.0) {
        streak++;
      } else {
        break;
      }
    }

    const progress = Math.min(100, (streak / target) * 100);
    return { current: streak, progress };
  },

  /**
   * 检查累计记录数
   */
  checkTotalRecords(records, target) {
    const current = records.length;
    const progress = Math.min(100, (current / target) * 100);
    return { current, progress };
  },

  /**
   * 显示徽章解锁提示
   */
  showAchievementUnlockToast(newlyUnlocked) {
    if (newlyUnlocked.length === 1) {
      wx.showToast({
        title: `🎉 解锁徽章：${newlyUnlocked[0].name}`,
        icon: 'none',
        duration: 3000
      });
    } else if (newlyUnlocked.length > 1) {
      wx.showToast({
        title: `🎉 解锁了 ${newlyUnlocked.length} 个徽章！`,
        icon: 'none',
        duration: 3000
      });
    }
  },

  /**
   * 设置模拟徽章数据（当真实数据不可用时）
   */
  async setMockAchievements() {
    try {
      // 获取当前的统计数据来生成更真实的徽章状态
      const { overview, qualityStats } = this.data;
      const totalComments = overview.totalComments || 0;
      const avgQuality = parseFloat(overview.avgQuality) || 0;

      // 从云数据库获取真实数据来计算进度
      const cloudService = getCloudService();

      const recordResult = await cloudService.getRecordList({ pageSize: 1000 });
      const records = recordResult.success ? recordResult.data : [];

      const commentResult = await cloudService.getCommentList({ pageSize: 1000 });
      const comments = commentResult.success ? commentResult.data : [];

      console.log('[报告页面] 模拟徽章数据基础:', {
        totalComments,
        avgQuality,
        records: records.length,
        comments: comments.length
      });

    // 计算单日记录数（今天的记录）
    const today = new Date().toDateString();
    const todayRecords = records.filter(record => {
      const recordDate = new Date(record.createTime || Date.now()).toDateString();
      return recordDate === today;
    });

    // 计算连续高质量评语数
    const sortedComments = comments.sort((a, b) => {
      const timeA = new Date(a.createTime || a.generateTime || 0).getTime();
      const timeB = new Date(b.createTime || b.generateTime || 0).getTime();
      return timeB - timeA;
    });

    let qualityStreak = 0;
    for (const comment of sortedComments) {
      const score = comment.score || 0;
      if (score >= 8.0) {
        qualityStreak++;
      } else {
        break;
      }
    }

    const mockAchievements = [
      {
        id: 'efficiency_master',
        name: '效率达人',
        description: '单日记录行为10条',
        icon: '🚀',
        isUnlocked: todayRecords.length >= 10,
        current: todayRecords.length,
        target: 10,
        progress: Math.min((todayRecords.length / 10) * 100, 100)
      },
      {
        id: 'quality_expert',
        name: '质量专家',
        description: '连续5次评语8分以上',
        icon: '⭐',
        isUnlocked: qualityStreak >= 5,
        current: qualityStreak,
        target: 5,
        progress: Math.min((qualityStreak / 5) * 100, 100)
      },
      {
        id: 'progress_mentor',
        name: '进步导师',
        description: '累计记录100条行为记录',
        icon: '📚',
        isUnlocked: records.length >= 100,
        current: records.length,
        target: 100,
        progress: Math.min((records.length / 100) * 100, 100)
      },
      {
        id: 'ai_education_expert',
        name: 'AI教育专家',
        description: '累计记录500条行为记录',
        icon: '👑',
        isUnlocked: records.length >= 500,
        current: records.length,
        target: 500,
        progress: Math.min((records.length / 500) * 100, 100)
      }
    ];

      this.setData({ achievements: mockAchievements });
      console.log('[报告页面] 设置基于真实数据的徽章状态完成:');
      mockAchievements.forEach(achievement => {
        console.log(`- ${achievement.icon} ${achievement.name}: ${achievement.current}/${achievement.target} (${achievement.progress.toFixed(1)}%)`);
      });

    } catch (error) {
      console.error('[报告页面] 设置模拟徽章数据失败:', error);

      // 如果获取云数据失败，使用默认的空数据
      const defaultAchievements = [
        {
          id: 'efficiency_master',
          name: '效率达人',
          description: '单日记录行为10条',
          icon: '🚀',
          isUnlocked: false,
          current: 0,
          target: 10,
          progress: 0
        },
        {
          id: 'quality_expert',
          name: '质量专家',
          description: '连续5次评语8分以上',
          icon: '⭐',
          isUnlocked: false,
          current: 0,
          target: 5,
          progress: 0
        },
        {
          id: 'progress_mentor',
          name: '进步导师',
          description: '累计记录100条行为记录',
          icon: '📚',
          isUnlocked: false,
          current: 0,
          target: 100,
          progress: 0
        },
        {
          id: 'ai_education_expert',
          name: 'AI教育专家',
          description: '累计记录500条行为记录',
          icon: '👑',
          isUnlocked: false,
          current: 0,
          target: 500,
          progress: 0
        }
      ];

      this.setData({ achievements: defaultAchievements });
    }
  },

  /**
   * 徽章点击事件
   */
  onAchievementTap(e) {
    const index = e.currentTarget.dataset.index;
    const achievement = this.data.achievements[index];
    
    if (!achievement) return;
    
    const progressText = achievement.isUnlocked ? 
      '✅ 已解锁' : 
      `进度: ${achievement.current}/${achievement.target} (${Math.round(achievement.progress || 0)}%)`;
    
    const unlockedTime = achievement.isUnlocked && achievement.unlockedAt ? 
      `\n🗓️ 解锁时间: ${new Date(achievement.unlockedAt).toLocaleString('zh-CN')}` : '';
    
    wx.showModal({
      title: `${achievement.icon} ${achievement.name}`,
      content: `━━━━━━━━━━━━━━━━━━━━━━━━
📋 解锁条件：
${achievement.description}

📊 当前状态：
${progressText}${unlockedTime}

💡 提示：
${this.getAchievementTip(achievement)}
━━━━━━━━━━━━━━━━━━━━━━━━`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 获取徽章提示
   */
  getAchievementTip(achievement) {
    if (achievement.isUnlocked) {
      return `恭喜您获得"${achievement.name}"徽章！这证明了您在AI辅助教学方面的卓越表现。`;
    }
    
    const tips = {
      efficiency_master: '建议：多记录学生的日常表现，观察细节，培养敏锐的教育洞察力。',
      quality_expert: '建议：在生成评语时注意个性化表达，结合具体事例，提升评语质量。',
      progress_mentor: '建议：坚持记录学生行为，积累教学数据，见证学生成长轨迹。',
      ai_education_expert: '建议：深度使用AI功能，探索更多教学场景，成为教育技术先锋。'
    };
    
    const tip = tips[achievement.id];
    if (tip) {
      return tip;
    }
    
    // 如果没有徽章管理器，显示通用提示
    if (!achievementManager) {
      return '徽章系统正在初始化中，继续使用应用来解锁更多成就！';
    }
    
    return '继续努力，您距离解锁这个徽章越来越近了！';
  },

  /**
   * 时间范围切换
   */
  onTimeRangeChange(e) {
    const timeRange = e.currentTarget.dataset.value;
    if (timeRange === this.data.currentTimeRange) return;

    this.setData({ currentTimeRange: timeRange });
    this.loadStatistics();
  },

  /**
   * 导出报告
   */
  exportReport() {
    wx.showActionSheet({
      itemList: ['导出为PDF', '导出为Excel', '生成分享图片'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.exportAsPDF();
            break;
          case 1:
            this.exportAsExcel();
            break;
          case 2:
            this.generateShareImage();
            break;
        }
      }
    });
  },

  /**
   * 导出为PDF
   */
  exportAsPDF() {
    wx.showLoading({ title: '生成PDF中...', mask: true });
    
    // 模拟导出过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: 'PDF导出成功',
        icon: 'success'
      });
    }, 2000);
  },

  /**
   * 导出为Excel
   */
  exportAsExcel() {
    wx.showLoading({ title: '生成Excel中...', mask: true });
    
    // 模拟导出过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: 'Excel导出成功',
        icon: 'success'
      });
    }, 2000);
  },



  /**
   * 洞察操作处理
   */
  onInsightAction(e) {
    const action = e.currentTarget.dataset.action;
    
    switch (action) {
      case 'viewQualityTrend':
        this.viewQualityTrend();
        break;
      case 'viewUncoveredStudents':
        this.viewUncoveredStudents();
        break;
    }
  },

  /**
   * 查看质量趋势
   */
  viewQualityTrend() {
    wx.showModal({
      title: '质量趋势',
      content: '您的评语质量在持续提升中，建议继续保持当前的评语风格和标准。',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 查看未覆盖学生
   */
  viewUncoveredStudents() {
    wx.navigateTo({
      url: '/pages/student/list/list?filter=uncovered'
    });
  },

  /**
   * 跳转到评语生成
   */
  goToCommentGenerate() {
    wx.navigateTo({
      url: '/pages/comment/generate/generate'
    });
  },

  /**
   * 跳转到学生列表
   */
  goToStudentList() {
    wx.navigateTo({
      url: '/pages/student/list/list'
    });
  },

  /**
   * 跳转到设置页面
   */
  goToSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  },

  /**
   * 分享报告
   */
  shareReport() {
    console.log('[分享] 用户点击分享报告按钮');

    // 显示确认对话框
    wx.showModal({
      title: '📊 生成分享图片',
      content: '即将为您生成精美的成长报告分享图片，包含您的核心数据和统计信息。\n\n生成完成后将自动保存到相册，您可以手动分享给朋友。',
      confirmText: '立即生成',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 直接生成分享图片
          this.generateShareImage();
        }
      }
    });
  },

  /**
   * 生成分享图片
   */
  async generateShareImage() {
    console.log('[分享] 开始生成分析报告分享图片');

    // 先刷新数据确保最新
    await this.refreshData();

    wx.showLoading({ title: '生成精美分享图片...', mask: true });

    try {
      const { overview, currentTimeRange, efficiencyStats, qualityDetails } = this.data;

      // 调试信息：打印当前数据
      console.log('[分享] 当前数据:', {
        overview,
        currentTimeRange,
        efficiencyStats,
        qualityDetails
      });

      // Canvas尺寸（调整高度以匹配新布局）
      const canvasWidth = 750;
      const canvasHeight = 1200;

      const ctx = wx.createCanvasContext('reportShareCanvas');

      // 清除Canvas缓存
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);

      // 1. 绘制精美渐变背景
      const gradient = ctx.createLinearGradient(0, 0, 0, canvasHeight);
      gradient.addColorStop(0, '#667eea');
      gradient.addColorStop(0.5, '#764ba2');
      gradient.addColorStop(1, '#f093fb');
      ctx.setFillStyle(gradient);
      ctx.fillRect(0, 0, canvasWidth, canvasHeight);

      // 2. 绘制装饰图形
      this.drawReportDecorations(ctx, canvasWidth, canvasHeight);

      // 3. 绘制主标题
      this.drawReportHeader(ctx, canvasWidth, currentTimeRange);

      // 4. 绘制核心数据
      this.drawReportStats(ctx, overview, canvasWidth);

      // 5. 绘制成就徽章
      this.drawAchievements(ctx, canvasWidth);

      // 6. 绘制底部信息
      this.drawReportFooter(ctx, canvasWidth, canvasHeight);

      // 绘制完成，生成图片
      ctx.draw(false, () => {
        setTimeout(() => {
          this.saveReportCanvasAsImage(canvasWidth, canvasHeight);
        }, 500);
      });

    } catch (error) {
      console.error('生成分析报告分享图片失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '生成失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 复制报告数据到剪贴板
   */
  copyReportToClipboard() {
    const { overview, efficiencyStats, qualityDetails, currentTimeRange } = this.data;
    const timeRangeText = currentTimeRange === 'week' ? '近7天' : 
                         currentTimeRange === 'month' ? '近30天' : '近3个月';
    
    const reportText = `📊 AI评语助手成长报告
━━━━━━━━━━━━━━━━━━━━━━━━
🗓️ 统计周期：${timeRangeText}
📅 生成时间：${new Date().toLocaleString('zh-CN')}

📈 核心数据概览：
  📝 生成评语：${overview.totalComments || 0}条
  ⏰ 节省时间：${overview.timeSaved || 0}小时  
  ⭐ 平均质量：${overview.avgQuality || 0}分
  🏆 优秀率：${overview.excellentRate || 0}%

⚡ 效率统计分析：
  🚀 平均生成时间：${efficiencyStats.avgTime || '0分钟'}
  🎯 一次通过率：${efficiencyStats.passRate || '0%'}
  📊 累计字数：${efficiencyStats.totalWords || '0'}字
  📅 连续使用：${efficiencyStats.consecutiveDays || '0天'}

🎯 质量详细分析：
  💼 专业度：${qualityDetails.professional || 0}分
  🎨 个性化：${qualityDetails.personalized || 0}分
  ✅ 完整性：${qualityDetails.completeness || 0}分

✨ 成长趋势：
  ${overview.commentsGrowth > 0 ? `📈 评语生成量比上期增长${overview.commentsGrowth}%` : '📊 评语生成稳定发展'}
  ${overview.qualityGrowth > 0 ? `🌟 评语质量比上期提升${overview.qualityGrowth}分` : '🎯 评语质量保持稳定'}

━━━━━━━━━━━━━━━━━━━━━━━━
🎉 评语灵感君 v1.0
让每一句评语都充满温度 💝`;

    wx.setClipboardData({
      data: reportText,
      success: () => {
        wx.showModal({
          title: '📋 复制成功',
          content: `📊 成长报告数据已复制到剪贴板！\n\n📱 您可以：\n• 粘贴到备忘录保存\n• 发送给同事朋友分享\n• 添加到工作汇报中\n• 制作成图片分享\n\n💡 数据包含${timeRangeText}的完整统计分析`,
          showCancel: false,
          confirmText: '知道了'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 分享到微信
   */
  shareToWeChat() {
    const { overview } = this.data;
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline'],
      success: () => {
        wx.showModal({
          title: '📱 微信分享',
          content: `🎯 准备分享您的成长报告！\n\n📊 报告亮点：\n• 已生成${overview.totalComments || 0}条优质评语\n• 累计节省${overview.timeSaved || 0}小时工作时间\n• 平均质量达到${overview.avgQuality || 0}分\n\n👥 分享价值：\n• 展示教学工作成果\n• 推广AI教育工具\n• 分享效率提升经验\n\n💡 点击右上角的"..."选择分享方式`,
          showCancel: false,
          confirmText: '去分享'
        });
      },
      fail: () => {
        wx.showToast({
          title: '分享功能暂不可用',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 保存报告到相册
   */
  saveReportToAlbum() {
    wx.showLoading({ title: '保存中...', mask: true });
    
    // 模拟保存过程
    setTimeout(() => {
      wx.hideLoading();
      
      wx.showModal({
        title: '📷 保存成功',
        content: `🎉 您的成长报告已保存到相册！\n\n📱 图片内容：\n• 个人成长数据总览\n• 精美的统计图表\n• 专业的报告样式\n• 可分享的高清画质\n\n💡 使用建议：\n• 发朋友圈展示成果\n• 添加到工作汇报\n• 制作教学展示材料\n• 与同事分享经验`,
        showCancel: false,
        confirmText: '知道了'
      });
    }, 1500);
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    const { overview } = this.data;
    return {
      title: `我的AI评语助手数据报告 - 已生成${overview.totalComments}条评语`,
      path: '/pages/analytics/report/report',
      imageUrl: '/assets/images/share-report.png'
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const { overview } = this.data;
    return {
      title: `AI评语助手使用报告 - 已节省${overview.timeSaved}小时工作时间`,
      query: '',
      imageUrl: '/assets/images/share-report.png'
    };
  },

  /**
   * 绘制报告装饰图形
   */
  drawReportDecorations(ctx, canvasWidth, canvasHeight) {
    // 绘制装饰圆圈
    ctx.setFillStyle('rgba(255, 255, 255, 0.08)');
    ctx.beginPath();
    ctx.arc(canvasWidth - 100, 150, 120, 0, 2 * Math.PI);
    ctx.fill();

    ctx.beginPath();
    ctx.arc(80, canvasHeight - 200, 100, 0, 2 * Math.PI);
    ctx.fill();

    // 绘制小圆点装饰
    ctx.setFillStyle('rgba(255, 255, 255, 0.15)');
    const dots = [
      { x: 150, y: 200, r: 8 },
      { x: 600, y: 300, r: 6 },
      { x: 100, y: 500, r: 10 },
      { x: 650, y: 800, r: 7 }
    ];

    dots.forEach(dot => {
      ctx.beginPath();
      ctx.arc(dot.x, dot.y, dot.r, 0, 2 * Math.PI);
      ctx.fill();
    });
  },

  /**
   * 绘制报告头部
   */
  drawReportHeader(ctx, canvasWidth, currentTimeRange) {
    const timeRangeText = currentTimeRange === 'week' ? '近7天' :
                         currentTimeRange === 'month' ? '近30天' : '近3个月';

    // 绘制主标题
    ctx.setFillStyle('#FFFFFF');
    ctx.setFontSize(48);
    ctx.setTextAlign('center');
    ctx.fillText('我的成长报告', canvasWidth / 2, 120);

    // 绘制副标题
    ctx.setFontSize(28);
    ctx.setFillStyle('rgba(255, 255, 255, 0.9)');
    ctx.fillText(timeRangeText + ' 数据分析', canvasWidth / 2, 170);

    // 绘制装饰线
    ctx.setStrokeStyle('rgba(255, 255, 255, 0.3)');
    ctx.setLineWidth(2);
    ctx.beginPath();
    ctx.moveTo(canvasWidth / 2 - 100, 190);
    ctx.lineTo(canvasWidth / 2 + 100, 190);
    ctx.stroke();
  },

  /**
   * 绘制报告统计数据
   */
  drawReportStats(ctx, overview, canvasWidth) {
    const startY = 250;
    const cardWidth = 160;
    const cardHeight = 120;
    const gap = 20;
    const totalWidth = cardWidth * 4 + gap * 3;
    const startX = (canvasWidth - totalWidth) / 2;

    const stats = [
      { label: '生成评语', value: `${overview.totalComments || 0}条`, color: '#FF6B6B' },
      { label: '节省时间', value: `${overview.timeSaved || 0}h`, color: '#4ECDC4' },
      { label: '平均质量', value: `${overview.avgQuality || 0}分`, color: '#45B7D1' },
      { label: '优秀率', value: `${overview.excellentRate || 0}%`, color: '#96CEB4' }
    ];

    stats.forEach((stat, index) => {
      const x = startX + index * (cardWidth + gap);
      const y = startY;

      // 绘制卡片背景
      ctx.setFillStyle('rgba(255, 255, 255, 0.95)');
      this.drawRoundedRect(ctx, x, y, cardWidth, cardHeight, 16);
      ctx.fill();

      // 绘制彩色顶部条
      ctx.setFillStyle(stat.color);
      this.drawRoundedRect(ctx, x, y, cardWidth, 8, 16);
      ctx.fill();

      // 绘制数值
      ctx.setFillStyle('#2C3E50');
      ctx.setFontSize(32);
      ctx.setTextAlign('center');
      ctx.fillText(stat.value, x + cardWidth / 2, y + 60);

      // 绘制标签
      ctx.setFontSize(20);
      ctx.setFillStyle('#7F8C8D');
      ctx.fillText(stat.label, x + cardWidth / 2, y + 90);
    });
  },

  /**
   * 绘制成就徽章区域
   */
  drawAchievements(ctx, canvasWidth) {
    const startY = 420;

    // 绘制区域标题
    ctx.setFillStyle('#FFFFFF');
    ctx.setFontSize(32);
    ctx.setTextAlign('center');
    ctx.fillText('🏆 我的成就', canvasWidth / 2, startY);

    // 获取已解锁的徽章（从本地存储）
    const unlockedAchievements = wx.getStorageSync('unlockedAchievements') || [];
    const unlockedIds = unlockedAchievements.map(a => a.id);

    // 成就徽章数据（与页面数据保持一致）
    const achievements = [
      { id: 'efficiency_master', icon: '🚀', name: '效率达人' },
      { id: 'quality_expert', icon: '⭐', name: '质量专家' },
      { id: 'progress_mentor', icon: '📚', name: '进步导师' },
      { id: 'ai_expert', icon: '👑', name: 'AI专家' }
    ];

    const badgeSize = 80;
    const gap = 40;
    const totalWidth = badgeSize * 4 + gap * 3;
    const startX = (canvasWidth - totalWidth) / 2;

    achievements.forEach((achievement, index) => {
      const x = startX + index * (badgeSize + gap);
      const y = startY + 60;
      const isUnlocked = unlockedIds.includes(achievement.id);

      // 绘制徽章背景圆圈
      ctx.beginPath();
      ctx.arc(x + badgeSize / 2, y + badgeSize / 2, badgeSize / 2, 0, 2 * Math.PI);

      if (isUnlocked) {
        // 已解锁：金色渐变背景
        ctx.setFillStyle('#FFD700');
      } else {
        // 未解锁：半透明背景
        ctx.setFillStyle('rgba(255, 255, 255, 0.2)');
      }
      ctx.fill();

      // 绘制徽章图标
      ctx.setFillStyle('#FFFFFF');
      ctx.setFontSize(36);
      ctx.setTextAlign('center');
      ctx.fillText(achievement.icon, x + badgeSize / 2, y + badgeSize / 2 + 12);

      // 绘制徽章名称
      ctx.setFillStyle('#FFFFFF');
      ctx.setFontSize(16);
      ctx.fillText(achievement.name, x + badgeSize / 2, y + badgeSize + 25);
    });
  },

  /**
   * 绘制报告底部信息
   */
  drawReportFooter(ctx, canvasWidth, canvasHeight) {
    const footerY = canvasHeight - 150;

    // 绘制分割线
    ctx.setStrokeStyle('rgba(255, 255, 255, 0.3)');
    ctx.setLineWidth(1);
    ctx.beginPath();
    ctx.moveTo(100, footerY);
    ctx.lineTo(canvasWidth - 100, footerY);
    ctx.stroke();

    // 绘制应用名称
    ctx.setFillStyle('#FFFFFF');
    ctx.setFontSize(28);
    ctx.setTextAlign('center');
    ctx.fillText('评语灵感君', canvasWidth / 2, footerY + 40);

    // 绘制slogan
    ctx.setFontSize(20);
    ctx.setFillStyle('rgba(255, 255, 255, 0.8)');
    ctx.fillText('让每一句评语都充满温度', canvasWidth / 2, footerY + 70);

    // 绘制生成时间
    const now = new Date().toLocaleString('zh-CN');
    ctx.setFontSize(16);
    ctx.setFillStyle('rgba(255, 255, 255, 0.6)');
    ctx.fillText(`生成时间：${now}`, canvasWidth / 2, footerY + 100);
  },

  /**
   * 保存报告Canvas为图片
   */
  saveReportCanvasAsImage(canvasWidth, canvasHeight) {
    // 添加时间戳避免缓存
    const timestamp = Date.now();
    wx.canvasToTempFilePath({
      canvasId: 'reportShareCanvas',
      width: canvasWidth,
      height: canvasHeight,
      destWidth: canvasWidth * 2,
      destHeight: canvasHeight * 2,
      fileType: 'png',
      quality: 1,
      success: (res) => {
        wx.hideLoading();
        console.log('分析报告分享图片生成成功:', res.tempFilePath);

        // 直接调用微信分享
        this.shareReportImageToWeChat(res.tempFilePath);
      },
      fail: (error) => {
        console.error('生成分析报告图片失败:', error);
        wx.hideLoading();
        wx.showToast({
          title: '生成图片失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 分享报告图片到微信
   */
  shareReportImageToWeChat(imagePath) {
    // 直接保存到相册（移除朋友/朋友圈选项，因为小程序无法直接调起微信分享界面）
    this.saveReportImageToAlbum(imagePath);
  },

  /**
   * 保存报告图片到相册
   */
  saveReportImageToAlbum(imagePath) {
    wx.saveImageToPhotosAlbum({
      filePath: imagePath,
      success: () => {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
      },
      fail: (error) => {
        if (error.errMsg.includes('auth')) {
          wx.showModal({
            title: '需要授权',
            content: '需要授权访问相册才能保存图片',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting();
              }
            }
          });
        } else {
          wx.showToast({
            title: '保存失败',
            icon: 'none'
          });
        }
      }
    });
  },

  /**
   * 绘制圆角矩形
   */
  drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
  }
});