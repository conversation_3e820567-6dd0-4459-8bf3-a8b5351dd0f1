{"collections": [{"name": "users", "description": "用户信息表", "fields": {"_id": "用户ID（OpenID）", "nickName": "用户姓名", "school": "学校名称", "role": "职位角色", "phone": "联系电话", "email": "邮箱地址", "avatar": "头像URL", "createTime": "创建时间", "updateTime": "更新时间"}, "indexes": [{"keys": {"createTime": -1}, "name": "createTime_-1"}]}, {"name": "classes", "description": "班级信息表", "fields": {"_id": "班级ID", "className": "班级名称", "grade": "年级", "subject": "学科", "description": "班级描述", "teacherId": "教师ID", "studentCount": "学生数量", "createTime": "创建时间", "updateTime": "更新时间"}, "indexes": [{"keys": {"teacherId": 1, "createTime": -1}, "name": "teacherId_1_createTime_-1"}]}, {"name": "students", "description": "学生信息表", "fields": {"_id": "学生ID", "name": "学生姓名", "studentId": "学号", "classId": "班级ID", "className": "班级名称", "gender": "性别", "phone": "联系电话", "remark": "备注信息", "teacherId": "教师ID", "createTime": "创建时间", "updateTime": "更新时间"}, "indexes": [{"keys": {"teacherId": 1, "classId": 1}, "name": "teacherId_1_classId_1"}, {"keys": {"studentId": 1}, "name": "studentId_1", "unique": true}]}, {"name": "records", "description": "行为记录表", "fields": {"_id": "记录ID", "studentId": "学生ID", "studentName": "学生姓名", "classId": "班级ID", "className": "班级名称", "type": "记录类型（positive/negative/neutral）", "category": "行为分类", "content": "记录内容", "images": "图片证据", "tags": "标签数组", "score": "评分", "teacherId": "教师ID", "createTime": "创建时间", "updateTime": "更新时间"}, "indexes": [{"keys": {"teacherId": 1, "createTime": -1}, "name": "teacherId_1_createTime_-1"}, {"keys": {"studentId": 1, "createTime": -1}, "name": "studentId_1_createTime_-1"}, {"keys": {"type": 1, "createTime": -1}, "name": "type_1_createTime_-1"}]}, {"name": "comments", "description": "评语表", "fields": {"_id": "评语ID", "studentId": "学生ID", "studentName": "学生姓名", "classId": "班级ID", "className": "班级名称", "content": "评语内容", "style": "评语风格", "length": "评语长度", "aiGenerated": "是否AI生成", "recordIds": "关联记录ID数组", "period": "评语周期", "status": "状态（draft/published）", "teacherId": "教师ID", "createTime": "创建时间", "updateTime": "更新时间"}, "indexes": [{"keys": {"teacherId": 1, "createTime": -1}, "name": "teacherId_1_createTime_-1"}, {"keys": {"studentId": 1, "createTime": -1}, "name": "studentId_1_createTime_-1"}]}, {"name": "settings", "description": "用户设置表", "fields": {"_id": "设置ID（用户ID）", "notifications": "通知设置", "preferences": "偏好设置", "aiConfig": "AI配置", "teacherId": "教师ID", "createTime": "创建时间", "updateTime": "更新时间"}, "indexes": [{"keys": {"teacherId": 1}, "name": "teacherId_1"}]}, {"name": "admins", "description": "管理员信息表", "fields": {"_id": "管理员ID", "username": "用户名", "password": "密码哈希", "email": "邮箱地址", "role": "角色（super_admin/admin）", "permissions": "权限数组", "status": "状态（active/inactive）", "profile": "个人资料对象", "createTime": "创建时间", "updateTime": "更新时间", "createTimestamp": "创建时间戳", "updateTimestamp": "更新时间戳", "lastLoginTime": "最后登录时间", "lastActiveTime": "最后活跃时间"}, "indexes": [{"keys": {"username": 1}, "name": "username_1", "unique": true}, {"keys": {"status": 1, "createTime": -1}, "name": "status_1_createTime_-1"}]}, {"name": "logs", "description": "操作日志表", "fields": {"_id": "日志ID", "action": "操作类型", "adminId": "管理员ID", "adminName": "管理员名称", "requestData": "请求数据", "result": "操作结果", "ip": "IP地址", "userAgent": "用户代理", "createTime": "创建时间", "timestamp": "时间戳"}, "indexes": [{"keys": {"adminId": 1, "createTime": -1}, "name": "adminId_1_createTime_-1"}, {"keys": {"action": 1, "createTime": -1}, "name": "action_1_createTime_-1"}]}]}