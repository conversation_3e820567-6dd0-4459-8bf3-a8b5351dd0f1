import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [
    react({
      // 启用Fast Refresh
      fastRefresh: true,
      babel: {
        compact: false,
        plugins: [
          // 生产环境移除console
          process.env.NODE_ENV === 'production' && [
            'transform-remove-console',
            { exclude: ['error', 'warn'] }
          ]
        ].filter(Boolean)
      }
    }),
    
    // 🔥 CSP安全策略插件
    {
      name: 'csp-html-plugin',
      transformIndexHtml(html) {
        // 开发环境需要更宽松的CSP策略
        const cspPolicy = process.env.NODE_ENV === 'development'
          ? "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' http://localhost:* ws://localhost:*; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:; connect-src 'self' http://localhost:* ws://localhost:* https://*.tcb.qcloud.la https://*.tencentcloudapi.com;"
          : "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:; connect-src 'self' https://*.tcb.qcloud.la https://*.tencentcloudapi.com;"
        
        return html.replace(
          '<head>',
          `<head>\n    <meta http-equiv="Content-Security-Policy" content="${cspPolicy}">`
        )
      }
    }
  ],
  
  // 路径别名
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      '@/hooks': path.resolve(__dirname, './src/hooks'),
      '@/services': path.resolve(__dirname, './src/services')
    }
  },

  // 开发服务器优化
  server: {
    host: '0.0.0.0',
    port: 3000,
    strictPort: true,
    open: false,
    cors: true,
    // 🔥 重要：启用HMR
    hmr: {
      overlay: false // 关闭错误遮罩层，提升性能
    },
    // 🔥 重要：关闭轮询，使用文件系统事件
    watch: {
      usePolling: false,
      interval: 100
    }
  },

  // 🔥 构建优化 - 极致性能配置
  build: {
    target: 'es2020',
    minify: 'esbuild', // 使用esbuild压缩，比terser快10-100倍
    sourcemap: false, // 生产环境不生成sourcemap
    cssMinify: true, // CSS压缩
    
    // 🔥 代码分割策略 - 根据实际使用情况优化
    rollupOptions: {
      output: {
        // 细粒度代码分割
        manualChunks: (id) => {
          // React相关
          if (id.includes('react') || id.includes('react-dom')) {
            return 'react-vendor'
          }
          
          // Antd UI库
          if (id.includes('antd') || id.includes('@ant-design')) {
            return 'ui-vendor'
          }
          
          // 图标库单独分包
          if (id.includes('@ant-design/icons')) {
            return 'icons-vendor'
          }
          
          // 工具库
          if (id.includes('axios') || id.includes('dayjs') || id.includes('zustand')) {
            return 'utils-vendor'
          }
          
          // 路由
          if (id.includes('react-router')) {
            return 'router-vendor'
          }
          
          // 图表库
          if (id.includes('echarts') || id.includes('chart')) {
            return 'charts-vendor'
          }
          
          // 性能优化库
          if (id.includes('react-window') || id.includes('react-virtualized')) {
            return 'virtualize-vendor'
          }
          
          // node_modules中的其他包
          if (id.includes('node_modules')) {
            return 'vendor'
          }
        },
        
        // 🔥 文件命名优化 - 更好的缓存策略
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.')
          const extType = info[info.length - 1]
          
          // 根据文件类型分目录
          if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
            return `assets/media/[name]-[hash].${extType}`
          }
          if (/\.(png|jpe?g|gif|svg|webp|avif)(\?.*)?$/i.test(assetInfo.name)) {
            return `assets/images/[name]-[hash].${extType}`
          }
          if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
            return `assets/fonts/[name]-[hash].${extType}`
          }
          return `assets/${extType}/[name]-[hash].${extType}`
        }
      },
      
      // 🔥 外部依赖处理 - 对于CDN资源
      external: (id) => {
        // 可以将大型库外部化，使用CDN
        // return ['react', 'react-dom'].includes(id)
        return false // 暂时不外部化，保持完整打包
      }
    },
    
    // 🔥 高级构建优化
    reportCompressedSize: false, // 禁用gzip大小报告，加快构建
    chunkSizeWarningLimit: 1000, // 提高chunk大小警告阈值
    assetsInlineLimit: 4096, // 小于4kb的资源内联为base64
    
    // 🔥 多线程构建
    commonjsOptions: {
      transformMixedEsModules: true, // 支持混合ES模块
    }
  },

  // 🔥 ESBuild极致优化 - 修复CSP问题
  esbuild: {
    target: 'es2020',
    jsxDev: false, // 生产环境关闭JSX开发模式
    drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : [],
    legalComments: 'none', // 移除法律注释
    minifyIdentifiers: true, // 压缩标识符
    minifySyntax: true, // 压缩语法
    minifyWhitespace: true, // 压缩空白字符
    treeShaking: true, // 启用tree shaking
    // 🔥 高级优化选项
    platform: 'browser',
    format: 'esm',
    keepNames: false, // 不保留函数名，减小体积
    pure: ['console.log', 'console.info', 'console.warn'], // 标记为无副作用，可被移除
    // 🔥 CSP兼容配置
    supported: {
      'dynamic-import': true,
      'import-meta': true,
    }
  },

  // 🔥 依赖预构建优化
  optimizeDeps: {
    force: false, // 不强制重新构建，使用缓存
    
    // 🔥 精确包含需要预构建的依赖
    include: [
      // 核心依赖
      'react',
      'react-dom',
      'react-dom/client',
      
      // UI库 - 按需包含
      'antd/es/button',
      'antd/es/card',
      'antd/es/row',
      'antd/es/col',
      'antd/es/statistic',
      'antd/es/typography',
      'antd/es/space',
      'antd/es/alert',
      'antd/es/skeleton',
      'antd/es/badge',
      'antd/es/progress',
      'antd/es/tooltip',
      'antd/es/input',
      'antd/es/empty',
      
      // 图标 - 只包含使用的图标
      '@ant-design/icons/UserOutlined',
      '@ant-design/icons/MessageOutlined',
      '@ant-design/icons/RobotOutlined',
      '@ant-design/icons/TrophyOutlined',
      '@ant-design/icons/ThunderboltOutlined',
      '@ant-design/icons/BarChartOutlined',
      '@ant-design/icons/LineChartOutlined',
      '@ant-design/icons/PieChartOutlined',
      '@ant-design/icons/EyeOutlined',
      '@ant-design/icons/RocketOutlined',
      '@ant-design/icons/SearchOutlined',
      
      // 工具库
      'axios',
      'zustand',
      'dayjs',
      'react-router-dom',
      
      // 性能优化库
      'react-window'
    ],
    
    // 🔥 排除不需要预构建的依赖
    exclude: [
      // 排除大型但不常用的库
      'echarts', // 按需导入
      '@ant-design/plots', // 如果使用的话
    ],
    
    // 🔥 ESBuild选项优化
    esbuildOptions: {
      target: 'es2020',
      jsx: 'automatic', // 使用新的JSX转换
      treeShaking: true,
      minify: false, // 预构建阶段不压缩，留给最终构建
      sourcemap: false, // 预构建不生成sourcemap
      define: {
        global: 'globalThis' // 修复global变量问题
      }
    }
  },

  // 环境变量
  define: {
    __DEV__: process.env.NODE_ENV === 'development',
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
    global: 'globalThis'
  }
})