/**
 * 豆包AI云函数
 * 调用豆包AI API生成评语
 */

const cloud = require('wx-server-sdk');
const axios = require('axios');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 豆包AI API默认配置
const DEFAULT_DOUBAO_CONFIG = {
  apiUrl: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
  model: 'doubao-pro-4k',
  temperature: 0.7,
  max_tokens: 500
};

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { prompt, model, temperature, max_tokens } = event;

  try {
    console.log('开始调用豆包AI，提示词长度:', prompt.length);

    // 从数据库获取AI配置
    const aiConfig = await getAIConfig();

    if (!aiConfig || !aiConfig.apiKey) {
      throw new Error('AI配置未设置或API密钥为空');
    }

    // 调用豆包AI API
    const response = await callDoubaoAPI({
      prompt,
      model: model || aiConfig.model || DEFAULT_DOUBAO_CONFIG.model,
      temperature: temperature || aiConfig.temperature || DEFAULT_DOUBAO_CONFIG.temperature,
      max_tokens: max_tokens || aiConfig.maxTokens || DEFAULT_DOUBAO_CONFIG.max_tokens,
      apiKey: aiConfig.apiKey,
      apiUrl: aiConfig.apiUrl || DEFAULT_DOUBAO_CONFIG.apiUrl
    });

    console.log('豆包AI调用成功');

    return {
      success: true,
      data: response,
      message: '生成成功'
    };

  } catch (error) {
    console.error('豆包AI调用失败:', error);

    // 返回智能备用方案
    const fallbackContent = generateFallbackContent(prompt);

    return {
      success: true,
      data: {
        content: fallbackContent
      },
      message: '使用备用方案生成',
      isFallback: true
    };
  }
};

/**
 * 从数据库获取AI配置
 */
async function getAIConfig() {
  try {
    const result = await db.collection('system_config')
      .where({
        type: 'ai_config',
        status: 'active'
      })
      .orderBy('updateTime', 'desc')
      .limit(1)
      .get();

    if (result.data && result.data.length > 0) {
      const config = result.data[0];
      console.log('获取AI配置成功:', {
        hasApiKey: !!config.apiKey,
        model: config.model,
        apiUrl: config.apiUrl
      });
      return config;
    } else {
      console.warn('未找到AI配置，使用默认配置');
      return null;
    }
  } catch (error) {
    console.error('获取AI配置失败:', error);
    return null;
  }
}

/**
 * 调用豆包AI API
 */
async function callDoubaoAPI({ prompt, model, temperature, max_tokens, apiKey, apiUrl }) {
  try {
    const requestData = {
      model: model,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: temperature,
      max_tokens: max_tokens,
      stream: false
    };

    console.log('调用豆包AI API:', {
      apiUrl,
      model,
      temperature,
      max_tokens,
      hasApiKey: !!apiKey
    });

    const response = await axios.post(apiUrl, requestData, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000 // 30秒超时
    });

    if (response.data && response.data.choices && response.data.choices[0]) {
      return {
        content: response.data.choices[0].message.content.trim(),
        usage: response.data.usage
      };
    } else {
      throw new Error('API响应格式错误');
    }

  } catch (error) {
    console.error('豆包AI API调用详细错误:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * 生成备用内容（当AI调用失败时）
 */
function generateFallbackContent(prompt) {
  // 从提示词中提取学生姓名
  const nameMatch = prompt.match(/姓名：(.+)/);
  const studentName = nameMatch ? nameMatch[1] : '该同学';

  // 从提示词中提取班级
  const classMatch = prompt.match(/班级：(.+)/);
  const className = classMatch ? classMatch[1] : '';

  // 根据风格生成不同的评语模板
  const templates = {
    formal: `${studentName}在本学期的学习中表现良好，能够按时完成各项学习任务。在专业技能方面有一定的基础，建议继续加强实践操作能力的培养。希望在新的学期里能够更加主动地参与课堂讨论，提升自己的综合素质。`,
    
    warm: `${studentName}是个很用心的孩子，这学期在学习上很努力，老师看在眼里很欣慰。你在专业课上的表现让老师印象深刻，继续保持这份认真劲儿。希望你能更加自信一些，相信自己一定能取得更好的成绩。`,
    
    encouraging: `${studentName}同学，你的潜力是无限的！这学期看到你在专业技能上的进步，老师为你感到骄傲。你有着很强的学习能力和实践天赋，只要继续努力，一定能在自己的专业道路上闯出一片天地。加油，未来属于你！`,
    
    detailed: `${studentName}在本学期的综合表现可以从以下几个方面来评价：学习态度端正，能够认真对待每一门课程；专业技能掌握较好，实训操作规范；与同学关系融洽，具有良好的团队合作精神。建议在理论学习方面加强深度思考，在实践操作中注重细节把控。期待你在下学期能够在创新思维和问题解决能力方面有更大突破。`
  };

  // 根据提示词中的风格选择模板
  if (prompt.includes('正式')) return templates.formal;
  if (prompt.includes('温和')) return templates.warm;
  if (prompt.includes('激励')) return templates.encouraging;
  if (prompt.includes('详细')) return templates.detailed;

  return templates.warm; // 默认使用温和风格
}
