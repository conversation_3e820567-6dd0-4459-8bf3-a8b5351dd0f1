/**
 * 环境变量加载器
 * 确保所有环境变量都正确加载
 */

const fs = require('fs');
const path = require('path');

class EnvLoader {
  constructor() {
    this.envVars = new Map();
    this.loadEnvFile();
  }

  /**
   * 加载.env文件
   */
  loadEnvFile() {
    const envPath = path.join(process.cwd(), '.env');
    
    if (!fs.existsSync(envPath)) {
      console.warn('⚠️ .env文件不存在，使用默认配置');
      return;
    }

    try {
      const envContent = fs.readFileSync(envPath, 'utf8');
      const lines = envContent.split('\n');

      lines.forEach(line => {
        line = line.trim();
        
        // 跳过注释和空行
        if (!line || line.startsWith('#')) {
          return;
        }

        const [key, ...valueParts] = line.split('=');
        const value = valueParts.join('=').trim();

        if (key && value) {
          this.envVars.set(key.trim(), value);
          process.env[key.trim()] = value;
        }
      });

      console.log('✅ 环境变量加载成功');
    } catch (error) {
      console.error('❌ 加载环境变量失败:', error.message);
    }
  }

  /**
   * 获取环境变量
   */
  get(key, defaultValue = null) {
    return process.env[key] || this.envVars.get(key) || defaultValue;
  }

  /**
   * 检查必需的环境变量
   */
  checkRequired(requiredVars) {
    const missing = [];

    requiredVars.forEach(varName => {
      if (!this.get(varName)) {
        missing.push(varName);
      }
    });

    if (missing.length > 0) {
      console.error('❌ 缺少必需的环境变量:', missing.join(', '));
      return false;
    }

    console.log('✅ 所有必需的环境变量都已配置');
    return true;
  }

  /**
   * 验证配置
   */
  validateConfig() {
    const requiredVars = [
      'MINIPROGRAM_APP_ID',
      'CLOUD_ENV_ID',
      'DOUBAO_API_KEY'
    ];

    const isValid = this.checkRequired(requiredVars);
    
    if (!isValid) {
      console.error('🚨 环境配置验证失败！请检查.env文件');
      return false;
    }

    // 验证具体值
    const appId = this.get('MINIPROGRAM_APP_ID');
    if (!appId.startsWith('wx')) {
      console.error('❌ MINIPROGRAM_APP_ID格式错误，应该以wx开头');
      return false;
    }

    const apiKey = this.get('DOUBAO_API_KEY');
    if (apiKey.includes('请填入')) {
      console.error('❌ DOUBAO_API_KEY未配置真实值');
      return false;
    }

    console.log('✅ 环境配置验证通过');
    return true;
  }

  /**
   * 获取云开发配置
   */
  getCloudConfig() {
    return {
      env: this.get('CLOUD_ENV_ID'),
      appid: this.get('MINIPROGRAM_APP_ID'),
      region: this.get('CLOUD_REGION', 'ap-shanghai'),
      traceUser: true,
      timeout: 60000
    };
  }

  /**
   * 获取AI配置
   */
  getAIConfig() {
    return {
      apiKey: this.get('DOUBAO_API_KEY'),
      apiUrl: this.get('DOUBAO_API_URL', 'https://ark.cn-beijing.volces.com/api/v3/chat/completions'),
      model: this.get('DOUBAO_MODEL', 'doubao-pro-4k'),
      temperature: parseFloat(this.get('AI_TEMPERATURE', '0.7')),
      maxTokens: parseInt(this.get('AI_MAX_TOKENS', '300'))
    };
  }
}

// 创建全局实例
const envLoader = new EnvLoader();

module.exports = {
  envLoader,
  EnvLoader
};
