# 彻底修复所有依赖问题

Write-Host "🚨 彻底修复依赖问题开始..." -ForegroundColor Red
Write-Host "======================================" -ForegroundColor Cyan
Write-Host ""

# 1. 完全清理
Write-Host "🧹 Step 1: 完全清理旧文件..." -ForegroundColor Yellow
if (Test-Path "node_modules") {
    Write-Host "删除 node_modules..." -ForegroundColor Gray
    Remove-Item -Path "node_modules" -Recurse -Force -ErrorAction SilentlyContinue
}

if (Test-Path "package-lock.json") {
    Write-Host "删除 package-lock.json..." -ForegroundColor Gray
    Remove-Item -Path "package-lock.json" -Force -ErrorAction SilentlyContinue
}

if (Test-Path "yarn.lock") {
    Write-Host "删除 yarn.lock..." -ForegroundColor Gray
    Remove-Item -Path "yarn.lock" -Force -ErrorAction SilentlyContinue
}

# 2. 修复 .npmrc 配置
Write-Host ""
Write-Host "⚙️ Step 2: 修复npm配置..." -ForegroundColor Yellow

$npmrcContent = @"
registry=https://registry.npmjs.org/
save-exact=false
package-lock=true
optional=true
legacy-peer-deps=false
audit-level=moderate
"@

$npmrcContent | Out-File -FilePath ".npmrc" -Encoding UTF8 -Force
Write-Host "✅ .npmrc 配置已更新" -ForegroundColor Green

# 3. 清理npm缓存
Write-Host ""
Write-Host "🔄 Step 3: 清理npm缓存..." -ForegroundColor Yellow
npm cache clean --force
Write-Host "✅ npm缓存已清理" -ForegroundColor Green

# 4. 检查Node.js版本
Write-Host ""
Write-Host "🔍 Step 4: 检查环境..." -ForegroundColor Yellow
$nodeVersion = node --version
$npmVersion = npm --version
Write-Host "Node.js版本: $nodeVersion" -ForegroundColor Cyan
Write-Host "npm版本: $npmVersion" -ForegroundColor Cyan

# 检查Node.js版本是否符合要求
$nodeVersionNumber = [version]($nodeVersion -replace 'v', '')
if ($nodeVersionNumber -lt [version]"16.0.0") {
    Write-Host "⚠️ 警告: Node.js版本过低，建议升级到16.0.0或更高版本" -ForegroundColor Red
}

# 5. 重新安装依赖
Write-Host ""
Write-Host "📦 Step 5: 重新安装依赖..." -ForegroundColor Yellow
Write-Host "这可能需要几分钟时间，请耐心等待..." -ForegroundColor Gray
Write-Host ""

$installSuccess = $false

# 尝试方法1: npm install
Write-Host "🔄 尝试方法1: npm install" -ForegroundColor Cyan
try {
    npm install --no-optional --no-audit
    if ($LASTEXITCODE -eq 0) {
        $installSuccess = $true
        Write-Host "✅ npm install 成功!" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ npm install 失败" -ForegroundColor Red
}

# 尝试方法2: npm install with legacy-peer-deps
if (-not $installSuccess) {
    Write-Host ""
    Write-Host "🔄 尝试方法2: npm install --legacy-peer-deps" -ForegroundColor Cyan
    try {
        npm install --legacy-peer-deps --no-audit
        if ($LASTEXITCODE -eq 0) {
            $installSuccess = $true
            Write-Host "✅ npm install --legacy-peer-deps 成功!" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ npm install --legacy-peer-deps 失败" -ForegroundColor Red
    }
}

# 尝试方法3: 使用yarn
if (-not $installSuccess) {
    Write-Host ""
    Write-Host "🔄 尝试方法3: 使用yarn" -ForegroundColor Cyan
    
    # 检查yarn是否安装
    try {
        $yarnVersion = yarn --version 2>$null
        Write-Host "检测到yarn版本: $yarnVersion" -ForegroundColor Gray
    } catch {
        Write-Host "安装yarn..." -ForegroundColor Gray
        npm install -g yarn --force
    }
    
    try {
        yarn install
        if ($LASTEXITCODE -eq 0) {
            $installSuccess = $true
            Write-Host "✅ yarn install 成功!" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ yarn install 失败" -ForegroundColor Red
    }
}

# 尝试方法4: 使用pnpm
if (-not $installSuccess) {
    Write-Host ""
    Write-Host "🔄 尝试方法4: 使用pnpm" -ForegroundColor Cyan
    
    try {
        $pnpmVersion = pnpm --version 2>$null
        Write-Host "检测到pnpm版本: $pnpmVersion" -ForegroundColor Gray
    } catch {
        Write-Host "安装pnpm..." -ForegroundColor Gray
        npm install -g pnpm --force
    }
    
    try {
        pnpm install
        if ($LASTEXITCODE -eq 0) {
            $installSuccess = $true
            Write-Host "✅ pnpm install 成功!" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ pnpm install 失败" -ForegroundColor Red
    }
}

# 6. 验证安装结果
Write-Host ""
Write-Host "🔍 Step 6: 验证安装结果..." -ForegroundColor Yellow

if ($installSuccess) {
    # 检查关键依赖是否存在
    $viteExists = Test-Path "node_modules\.bin\vite.cmd"
    $reactExists = Test-Path "node_modules\react"
    $typescriptExists = Test-Path "node_modules\typescript"
    
    Write-Host "检查关键依赖:" -ForegroundColor Gray
    Write-Host "  vite: $(if($viteExists){'✅'}else{'❌'})" -ForegroundColor $(if($viteExists){'Green'}else{'Red'})
    Write-Host "  react: $(if($reactExists){'✅'}else{'❌'})" -ForegroundColor $(if($reactExists){'Green'}else{'Red'})
    Write-Host "  typescript: $(if($typescriptExists){'✅'}else{'❌'})" -ForegroundColor $(if($typescriptExists){'Green'}else{'Red'})
    
    if ($viteExists -and $reactExists -and $typescriptExists) {
        Write-Host ""
        Write-Host "🎉 所有依赖安装成功!" -ForegroundColor Green
        Write-Host ""
        Write-Host "📋 接下来的步骤:" -ForegroundColor Cyan
        Write-Host "1. 验证环境配置: node scripts\validate-env.js" -ForegroundColor White
        Write-Host "2. 启动开发服务器: npm run dev" -ForegroundColor White
        Write-Host ""
        
        # 询问是否立即启动
        $response = Read-Host "是否立即启动开发服务器? (y/n)"
        if ($response -eq 'y' -or $response -eq 'Y' -or $response -eq '') {
            Write-Host ""
            Write-Host "🚀 启动开发服务器..." -ForegroundColor Green
            npm run dev
        }
    } else {
        Write-Host ""
        Write-Host "⚠️ 部分依赖可能有问题，但可以尝试启动" -ForegroundColor Yellow
    }
} else {
    Write-Host ""
    Write-Host "❌ 所有安装方法都失败了" -ForegroundColor Red
    Write-Host ""
    Write-Host "🛠️ 手动解决方案:" -ForegroundColor Yellow
    Write-Host "1. 检查网络连接" -ForegroundColor White
    Write-Host "2. 尝试使用VPN或更换网络" -ForegroundColor White
    Write-Host "3. 检查防火墙设置" -ForegroundColor White
    Write-Host "4. 升级Node.js到最新LTS版本" -ForegroundColor White
}

Write-Host ""
Write-Host "======================================" -ForegroundColor Cyan
Write-Host "按任意键退出..." -ForegroundColor Gray
Read-Host