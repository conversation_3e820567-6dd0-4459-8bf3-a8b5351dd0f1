@echo off
chcp 65001 > nul
title 🚀 快速启动优化版管理后台

echo.
echo ========================================
echo    🚀 评语灵感君 - 管理后台启动器
echo ========================================
echo.

echo 📋 清理缓存中...
if exist node_modules\.vite (
    echo ✅ 清理Vite缓存
    rmdir /s /q node_modules\.vite
)

if exist node_modules\.cache (
    echo ✅ 清理构建缓存
    rmdir /s /q node_modules\.cache
)

echo.
echo 🚀 启动开发服务器...
echo.
echo 💡 提示：
echo   - 页面右上角有"诊断"按钮可以打开性能工具
echo   - 当FPS低于30时会自动显示性能警告
echo   - 性能工具会实时监控并给出优化建议
echo.

npm run dev

pause