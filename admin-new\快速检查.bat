@echo off
echo =============================================
echo   快速检查当前状态
echo =============================================
echo.

echo 🔍 检查关键文件:
if exist "node_modules" (
    echo ✅ node_modules 存在
    echo    大小: 
    dir node_modules | findstr "个文件"
) else (
    echo ❌ node_modules 不存在
)

if exist "package-lock.json" (
    echo ✅ package-lock.json 存在
) else (
    echo ❌ package-lock.json 不存在
)

echo.
echo 🔍 检查关键依赖:
if exist "node_modules\vite" (
    echo ✅ Vite 已安装
) else (
    echo ❌ Vite 未安装
)

if exist "node_modules\rollup" (
    echo ✅ Rollup 已安装
) else (
    echo ❌ Rollup 未安装
)

echo.
echo 🚀 尝试直接启动:
echo 执行 npm run dev...
npm run dev

echo.
echo =============================================
echo   按任意键退出...
echo =============================================
pause