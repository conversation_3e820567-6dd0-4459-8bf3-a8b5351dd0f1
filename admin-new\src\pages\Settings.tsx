import React, { useState } from 'react'
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Space, 
  Typography, 
  Tabs, 
  Switch,
  InputNumber,
  Select,
  message,
  Divider,
  Avatar,
  Upload
} from 'antd'
import { UserOutlined, UploadOutlined, SaveOutlined } from '@ant-design/icons'
import { useAuthStore } from '../stores/authStore'
import MiniProgramConnection from '../components/MiniProgramConnection'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select
const { TabPane } = Tabs

const Settings: React.FC = () => {
  const [form] = Form.useForm()
  const [profileForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const { user } = useAuthStore()

  const onSystemSettingsFinish = async (values: any) => {
    setLoading(true)
    try {
      console.log('系统设置:', values)
      message.success('系统设置保存成功！')
    } catch (error) {
      message.error('保存失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const onProfileFinish = async (values: any) => {
    setLoading(true)
    try {
      console.log('个人信息:', values)
      message.success('个人信息更新成功！')
    } catch (error) {
      message.error('更新失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const uploadProps = {
    name: 'avatar',
    action: '/api/upload/avatar',
    showUploadList: false,
    onChange(info: any) {
      if (info.file.status === 'done') {
        message.success('头像上传成功')
      } else if (info.file.status === 'error') {
        message.error('头像上传失败')
      }
    }
  }

  return (
    <div>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <div>
          <Title level={2} style={{ marginBottom: 8 }}>
            系统设置
          </Title>
          <Text type="secondary">
            管理系统配置和个人信息
          </Text>
        </div>

        <Tabs defaultActiveKey="profile" type="card">
          <TabPane tab="个人信息" key="profile">
            <Card title="个人资料" style={{ maxWidth: 800 }}>
              <div style={{ display: 'flex', alignItems: 'flex-start', gap: 24, marginBottom: 24 }}>
                <div style={{ textAlign: 'center' }}>
                  <Avatar size={80} icon={<UserOutlined />} />
                  <div style={{ marginTop: 12 }}>
                    <Upload {...uploadProps}>
                      <Button size="small" icon={<UploadOutlined />}>
                        更换头像
                      </Button>
                    </Upload>
                  </div>
                </div>
                <div style={{ flex: 1 }}>
                  <Form
                    form={profileForm}
                    layout="vertical"
                    onFinish={onProfileFinish}
                    initialValues={{
                      name: user?.name || '',
                      username: user?.username || '',
                      email: user?.email || '',
                      phone: '',
                      description: ''
                    }}
                  >
                    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: 16 }}>
                      <Form.Item
                        label="姓名"
                        name="name"
                        rules={[{ required: true, message: '请输入姓名' }]}
                      >
                        <Input />
                      </Form.Item>

                      <Form.Item
                        label="用户名"
                        name="username"
                        rules={[{ required: true, message: '请输入用户名' }]}
                      >
                        <Input disabled />
                      </Form.Item>

                      <Form.Item
                        label="邮箱"
                        name="email"
                        rules={[
                          { required: true, message: '请输入邮箱' },
                          { type: 'email', message: '请输入有效的邮箱' }
                        ]}
                      >
                        <Input />
                      </Form.Item>

                      <Form.Item
                        label="手机号"
                        name="phone"
                      >
                        <Input />
                      </Form.Item>
                    </div>

                    <Form.Item
                      label="个人描述"
                      name="description"
                    >
                      <TextArea rows={3} placeholder="简单介绍一下自己..." />
                    </Form.Item>

                    <Form.Item>
                      <Button type="primary" htmlType="submit" loading={loading} icon={<SaveOutlined />}>
                        保存个人信息
                      </Button>
                    </Form.Item>
                  </Form>
                </div>
              </div>
            </Card>

            <Card title="密码修改" style={{ maxWidth: 800, marginTop: 16 }}>
              <Form
                layout="vertical"
                onFinish={(values) => {
                  console.log('修改密码:', values)
                  message.success('密码修改成功！')
                }}
              >
                <Form.Item
                  label="当前密码"
                  name="currentPassword"
                  rules={[{ required: true, message: '请输入当前密码' }]}
                >
                  <Input.Password />
                </Form.Item>

                <Form.Item
                  label="新密码"
                  name="newPassword"
                  rules={[
                    { required: true, message: '请输入新密码' },
                    { min: 6, message: '密码长度至少6位' }
                  ]}
                >
                  <Input.Password />
                </Form.Item>

                <Form.Item
                  label="确认新密码"
                  name="confirmPassword"
                  dependencies={['newPassword']}
                  rules={[
                    { required: true, message: '请确认新密码' },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('newPassword') === value) {
                          return Promise.resolve()
                        }
                        return Promise.reject(new Error('两次输入的密码不一致'))
                      }
                    })
                  ]}
                >
                  <Input.Password />
                </Form.Item>

                <Form.Item>
                  <Button type="primary" htmlType="submit">
                    修改密码
                  </Button>
                </Form.Item>
              </Form>
            </Card>
          </TabPane>

          <TabPane tab="系统配置" key="system">
            <Card title="基础设置" style={{ marginBottom: 16 }}>
              <Form
                form={form}
                layout="vertical"
                onFinish={onSystemSettingsFinish}
                initialValues={{
                  systemName: '评语灵感君管理后台',
                  version: 'v2.0.0',
                  maxUsers: 1000,
                  sessionTimeout: 30,
                  enableRegistration: false,
                  enableEmailNotify: true,
                  enableSMSNotify: false,
                  maintenanceMode: false,
                  logLevel: 'info',
                  backupFrequency: 'daily'
                }}
              >
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: 16 }}>
                  <Form.Item
                    label="系统名称"
                    name="systemName"
                    rules={[{ required: true, message: '请输入系统名称' }]}
                  >
                    <Input />
                  </Form.Item>

                  <Form.Item
                    label="系统版本"
                    name="version"
                  >
                    <Input disabled />
                  </Form.Item>

                  <Form.Item
                    label="最大用户数"
                    name="maxUsers"
                    tooltip="系统支持的最大用户数量"
                  >
                    <InputNumber min={1} max={10000} style={{ width: '100%' }} />
                  </Form.Item>

                  <Form.Item
                    label="会话超时(分钟)"
                    name="sessionTimeout"
                    tooltip="用户无操作后自动登出的时间"
                  >
                    <InputNumber min={5} max={120} style={{ width: '100%' }} />
                  </Form.Item>

                  <Form.Item
                    label="日志级别"
                    name="logLevel"
                  >
                    <Select>
                      <Option value="debug">Debug</Option>
                      <Option value="info">Info</Option>
                      <Option value="warn">Warning</Option>
                      <Option value="error">Error</Option>
                    </Select>
                  </Form.Item>

                  <Form.Item
                    label="备份频率"
                    name="backupFrequency"
                  >
                    <Select>
                      <Option value="hourly">每小时</Option>
                      <Option value="daily">每天</Option>
                      <Option value="weekly">每周</Option>
                      <Option value="monthly">每月</Option>
                    </Select>
                  </Form.Item>
                </div>

                <Divider />

                <Title level={4}>功能开关</Title>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: 16 }}>
                  <Form.Item
                    label="允许用户注册"
                    name="enableRegistration"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="邮件通知"
                    name="enableEmailNotify"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="短信通知"
                    name="enableSMSNotify"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="维护模式"
                    name="maintenanceMode"
                    valuePropName="checked"
                    tooltip="开启后只有管理员可以访问系统"
                  >
                    <Switch />
                  </Form.Item>
                </div>

                <Form.Item style={{ marginTop: 24 }}>
                  <Space>
                    <Button type="primary" htmlType="submit" loading={loading} icon={<SaveOutlined />}>
                      保存配置
                    </Button>
                    <Button onClick={() => form.resetFields()}>
                      重置
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </Card>
          </TabPane>

          <TabPane tab="通知设置" key="notifications">
            <Card title="通知配置">
              <Form
                layout="vertical"
                initialValues={{
                  emailEnabled: true,
                  smsEnabled: false,
                  browserEnabled: true,
                  systemAlert: true,
                  userActivity: false,
                  dataBackup: true,
                  errorAlert: true
                }}
                onFinish={(values) => {
                  console.log('通知设置:', values)
                  message.success('通知设置保存成功！')
                }}
              >
                <Title level={4}>通知方式</Title>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 16, marginBottom: 24 }}>
                  <Form.Item
                    label="邮件通知"
                    name="emailEnabled"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="短信通知"
                    name="smsEnabled"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="浏览器推送"
                    name="browserEnabled"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </div>

                <Divider />

                <Title level={4}>通知类型</Title>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 16 }}>
                  <Form.Item
                    label="系统告警"
                    name="systemAlert"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="用户活动"
                    name="userActivity"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="数据备份"
                    name="dataBackup"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="错误报告"
                    name="errorAlert"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </div>

                <Form.Item style={{ marginTop: 24 }}>
                  <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                    保存通知设置
                  </Button>
                </Form.Item>
              </Form>
            </Card>
          </TabPane>

          <TabPane tab="小程序连接" key="miniprogram">
            <Card>
              <MiniProgramConnection />
            </Card>
          </TabPane>
        </Tabs>
      </Space>
    </div>
  )
}

export default Settings