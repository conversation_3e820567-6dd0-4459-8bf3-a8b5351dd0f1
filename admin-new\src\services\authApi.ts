import { apiClient } from './apiClient'
import { env } from '@/utils/env'

interface LoginCredentials {
  username: string
  password: string
}

interface User {
  id: string
  name: string
  username: string
  email?: string
  role: string
  permissions: string[]
  status: string
  profile?: {
    name: string
    avatar?: string
    phone?: string
    department?: string
  }
}

interface LoginResponse {
  user: User
  token: string
}

interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: string
}

export const authApi = {
  /**
   * 管理员登录
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      // 如果启用模拟模式，使用模拟数据
      if (env.ENABLE_MOCK) {
        return await this.mockLogin(credentials)
      }

      // 真实API调用
      const response = await apiClient.post<ApiResponse<LoginResponse>>('', {
        action: 'auth.login',
        username: credentials.username,
        password: credentials.password,
        timestamp: Date.now(),
        requestId: `login_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      })

      if (response.data.code !== 200) {
        throw new Error(response.data.message || '登录失败')
      }

      return response.data.data
    } catch (error: any) {
      console.error('Login error:', error)
      
      // 处理网络错误或API错误
      if (error.response?.data?.message) {
        throw new Error(error.response.data.message)
      } else if (error.message) {
        throw new Error(error.message)
      } else {
        throw new Error('登录失败，请检查网络连接')
      }
    }
  },

  /**
   * 退出登录
   */
  async logout(): Promise<void> {
    try {
      if (env.ENABLE_MOCK) {
        // 模拟退出登录
        await new Promise(resolve => setTimeout(resolve, 500))
        return
      }

      // 真实API调用
      await apiClient.post('', {
        action: 'auth.logout',
        timestamp: Date.now(),
        requestId: `logout_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      })
    } catch (error) {
      console.warn('Logout error:', error)
      // 即使退出登录失败，也不阻止用户
    }
  },

  /**
   * 验证Token有效性
   */
  async validateToken(token: string): Promise<boolean> {
    try {
      if (env.ENABLE_MOCK) {
        // 模拟Token验证
        return Promise.resolve(!!token && token.startsWith('mock_jwt_token_'))
      }

      if (!token) {
        return false
      }

      // 真实API调用
      const response = await apiClient.post<ApiResponse<{ valid: boolean }>>('', {
        action: 'auth.validateToken',
        token,
        timestamp: Date.now(),
        requestId: `validate_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      })

      return response.data.code === 200 && response.data.data.valid
    } catch (error) {
      console.warn('Token validation error:', error)
      return false
    }
  },

  /**
   * 刷新Token
   */
  async refreshToken(): Promise<{ token: string; user: User }> {
    try {
      if (env.ENABLE_MOCK) {
        // 模拟Token刷新
        return {
          token: 'mock_jwt_token_' + Date.now(),
          user: {
            id: '1',
            name: '系统管理员',
            username: 'admin',
            email: '<EMAIL>',
            role: 'super_admin',
            permissions: ['*'],
            status: 'active'
          }
        }
      }

      // 真实API调用
      const response = await apiClient.post<ApiResponse<{ token: string; user: User }>>('', {
        action: 'auth.refreshToken',
        timestamp: Date.now(),
        requestId: `refresh_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      })

      if (response.data.code !== 200) {
        throw new Error(response.data.message || 'Token刷新失败')
      }

      return response.data.data
    } catch (error: any) {
      console.error('Token refresh error:', error)
      throw new Error(error.response?.data?.message || error.message || 'Token刷新失败')
    }
  },

  /**
   * 获取用户信息
   */
  async getUserInfo(): Promise<User> {
    try {
      if (env.ENABLE_MOCK) {
        // 模拟用户信息
        return {
          id: '1',
          name: '系统管理员',
          username: 'admin',
          email: '<EMAIL>',
          role: 'super_admin',
          permissions: ['*'],
          status: 'active',
          profile: {
            name: '系统管理员',
            department: '系统管理部'
          }
        }
      }

      // 真实API调用
      const response = await apiClient.post<ApiResponse<User>>('', {
        action: 'auth.getUserInfo',
        timestamp: Date.now(),
        requestId: `userinfo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      })

      if (response.data.code !== 200) {
        throw new Error(response.data.message || '获取用户信息失败')
      }

      return response.data.data
    } catch (error: any) {
      console.error('Get user info error:', error)
      throw new Error(error.response?.data?.message || error.message || '获取用户信息失败')
    }
  },

  /**
   * 检查是否已有管理员
   */
  async checkAdminExists(): Promise<{ hasAdmin: boolean; count: number }> {
    try {
      // 真实API调用（不使用模拟数据，这个接口需要真实检查）
      const response = await apiClient.post<ApiResponse<{ hasAdmin: boolean; count: number }>>('', {
        action: 'auth.checkAdminExists',
        timestamp: Date.now(),
        requestId: `check_admin_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      })

      if (response.data.code !== 200) {
        throw new Error(response.data.message || '检查管理员状态失败')
      }

      return response.data.data
    } catch (error: any) {
      console.error('Check admin exists error:', error)
      // 如果检查失败，默认认为已有管理员（安全策略）
      return { hasAdmin: true, count: 1 }
    }
  },

  /**
   * 初始化管理员账户
   */
  async initAdmin(adminData: {
    username: string
    password: string
    email?: string
    profile?: {
      name?: string
      phone?: string
      department?: string
    }
  }): Promise<{ id: string; username: string; message: string }> {
    try {
      // 真实API调用（不使用模拟数据）
      const response = await apiClient.post<ApiResponse<{ id: string; username: string; message: string }>>('', {
        action: 'auth.initAdmin',
        ...adminData,
        timestamp: Date.now(),
        requestId: `init_admin_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      })

      if (response.data.code !== 200) {
        throw new Error(response.data.message || '初始化管理员失败')
      }

      return response.data.data
    } catch (error: any) {
      console.error('Init admin error:', error)
      throw new Error(error.response?.data?.message || error.message || '初始化管理员失败')
    }
  },

  /**
   * 模拟登录（开发环境使用）
   */
  async mockLogin(credentials: LoginCredentials): Promise<LoginResponse> {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (credentials.username === 'admin' && credentials.password === 'admin123') {
      return {
        user: {
          id: '1',
          name: '系统管理员',
          username: 'admin',
          email: '<EMAIL>',
          role: 'super_admin',
          permissions: ['*'],
          status: 'active',
          profile: {
            name: '系统管理员',
            department: '系统管理部'
          }
        },
        token: 'mock_jwt_token_' + Date.now()
      }
    } else {
      throw new Error('用户名或密码错误')
    }
  }
}