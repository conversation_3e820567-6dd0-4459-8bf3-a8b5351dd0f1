@echo off
chcp 65001 > nul
title 评语灵感君 - 管理员初始化工具

echo.
echo ========================================
echo   评语灵感君管理后台 - 管理员初始化
echo ========================================
echo.

rem 检查Node.js是否安装
node --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未检测到Node.js
    echo.
    echo 请先安装Node.js：
    echo 1. 访问 https://nodejs.org/
    echo 2. 下载并安装LTS版本
    echo 3. 重新运行此脚本
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js 环境检查通过
echo.

rem 运行初始化脚本
echo 🚀 启动管理员初始化流程...
echo.

cd /d "%~dp0"
node scripts\init-admin.js

if %errorlevel% equ 0 (
    echo.
    echo ✅ 初始化完成！
) else (
    echo.
    echo ❌ 初始化失败，请检查错误信息
)

echo.
echo 按任意键关闭窗口...
pause > nul