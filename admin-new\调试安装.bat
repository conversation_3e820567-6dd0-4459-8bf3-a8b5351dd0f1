@echo off
echo =============================================
echo   调试版安装脚本 - 显示所有信息
echo =============================================
echo.

echo 🔍 系统信息检查
echo Node.js版本:
node --version
echo npm版本:
npm --version
echo 当前目录:
cd
echo.

echo 🔍 检查现有文件
if exist "node_modules" (
    echo ❌ node_modules 存在
) else (
    echo ✅ node_modules 不存在
)

if exist "package-lock.json" (
    echo ❌ package-lock.json 存在
) else (
    echo ✅ package-lock.json 不存在
)
echo.

echo 🧹 开始清理...
echo 删除 node_modules...
if exist "node_modules" rmdir /s /q node_modules
echo 删除 package-lock.json...
if exist "package-lock.json" del package-lock.json
echo ✅ 清理完成
echo.

echo 🔄 清理npm缓存...
npm cache clean --force
echo ✅ 缓存清理完成
echo.

echo 📦 开始安装依赖...
echo 执行: npm install
npm install

echo.
echo 📊 安装结果检查:
if errorlevel 1 (
    echo ❌ npm install 失败，错误代码: %errorlevel%
    echo.
    echo 🔄 尝试强制安装...
    npm install --force
    
    if errorlevel 1 (
        echo ❌ 强制安装也失败，错误代码: %errorlevel%
        echo.
        echo 🔄 最后尝试: 跳过peer依赖检查...
        npm install --legacy-peer-deps
        
        if errorlevel 1 (
            echo ❌ 所有安装方法都失败了
            goto :error_summary
        ) else (
            echo ✅ legacy-peer-deps 方式安装成功！
            goto :success_check
        )
    ) else (
        echo ✅ 强制安装成功！
        goto :success_check
    )
) else (
    echo ✅ 标准安装成功！
    goto :success_check
)

:success_check
echo.
echo 🔍 验证关键依赖:
if exist "node_modules\vite" (
    echo ✅ Vite 已安装
) else (
    echo ❌ Vite 未找到
)

if exist "node_modules\rollup" (
    echo ✅ Rollup 已安装
) else (
    echo ❌ Rollup 未找到
)

if exist "node_modules\react" (
    echo ✅ React 已安装
) else (
    echo ❌ React 未找到
)

if exist "node_modules\typescript" (
    echo ✅ TypeScript 已安装
) else (
    echo ❌ TypeScript 未找到
)

echo.
echo 🚀 尝试启动开发服务器...
echo 执行: npm run dev
echo.
npm run dev

goto :end

:error_summary
echo.
echo =============================================
echo   ❌ 安装失败总结
echo =============================================
echo.
echo 可能的原因:
echo 1. 网络连接问题
echo 2. npm registry 问题
echo 3. 权限问题
echo 4. 磁盘空间不足
echo.
echo 建议解决方案:
echo 1. 检查网络连接
echo 2. 切换npm源: npm config set registry https://registry.npmmirror.com/
echo 3. 以管理员身份运行
echo 4. 检查磁盘空间
echo.

:end
echo.
echo =============================================
echo   按任意键继续...
echo =============================================
pause