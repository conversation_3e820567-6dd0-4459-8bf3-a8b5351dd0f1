@echo off
echo =============================================
echo   终极解决方案 - Node.js版本兼容性修复
echo =============================================
echo.

echo 🎯 你的Node.js版本是v22.17.1，这个版本太新了！
echo 💡 我们需要降级到稳定的LTS版本或者强制兼容
echo.

echo 方案A: 使用--force强制兼容（推荐）
echo =============================================

echo 步骤1: 完全清理
if exist "node_modules" rmdir /s /q node_modules
if exist "package-lock.json" del package-lock.json

echo 步骤2: 设置兼容性配置
npm config set legacy-peer-deps true
npm config set optional false
npm config set engine-strict false

echo 步骤3: 强制安装所有依赖
npm install --force --legacy-peer-deps --no-optional

echo 步骤4: 如果还报错，直接跳过可选依赖
if errorlevel 1 (
    echo 跳过可选依赖，重新安装...
    npm install --force --legacy-peer-deps --omit=optional
)

echo 步骤5: 手动修复vite配置
echo 创建更兼容的vite配置...

echo 步骤6: 尝试启动
npm run dev

echo.
echo =============================================
echo 如果还不行，请告诉我具体错误信息！
echo =============================================
pause

echo.
echo 方案B: 降级Node.js版本（如果方案A失败）
echo =============================================
echo 1. 下载Node.js 18.x LTS版本
echo 2. 卸载当前Node.js v22.17.1
echo 3. 安装Node.js 18.x
echo 4. 重新运行此脚本
echo.
pause