/**
 * 实时数据更新Hook
 * 用于管理WebSocket连接和实时数据更新
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { RealtimeConnection } from '../services/miniProgramApi'

interface RealtimeData {
  type: 'user_activity' | 'tokens_update' | 'comment_generated' | 'system_status'
  payload: any
  timestamp: number
}

interface UseRealTimeDataOptions {
  enabled?: boolean
  onUserActivity?: (data: any) => void
  onTokensUpdate?: (data: any) => void
  onCommentGenerated?: (data: any) => void
  onSystemStatus?: (data: any) => void
}

export const useRealTimeData = (options: UseRealTimeDataOptions = {}) => {
  const { enabled = true, onUserActivity, onTokensUpdate, onCommentGenerated, onSystemStatus } = options
  
  const [isConnected, setIsConnected] = useState(false)
  const [connectionError, setConnectionError] = useState<string | null>(null)
  const [lastUpdate, setLastUpdate] = useState<number>(0)
  
  const connectionRef = useRef<RealtimeConnection | null>(null)
  
  // 处理接收到的实时数据
  const handleMessage = useCallback((data: RealtimeData) => {
    console.log('收到实时数据:', data)
    
    setLastUpdate(Date.now())
    
    switch (data.type) {
      case 'user_activity':
        onUserActivity?.(data.payload)
        break
      case 'tokens_update':
        onTokensUpdate?.(data.payload)
        break
      case 'comment_generated':
        onCommentGenerated?.(data.payload)
        break
      case 'system_status':
        onSystemStatus?.(data.payload)
        break
      default:
        console.log('未知的实时数据类型:', data.type)
    }
  }, [onUserActivity, onTokensUpdate, onCommentGenerated, onSystemStatus])
  
  // 处理连接错误
  const handleError = useCallback((error: any) => {
    console.error('实时连接错误:', error)
    setConnectionError(error.message || '连接错误')
    setIsConnected(false)
  }, [])
  
  // 建立连接
  const connect = useCallback(() => {
    if (!enabled || connectionRef.current) return
    
    console.log('建立实时数据连接...')
    
    const connection = new RealtimeConnection()
    connectionRef.current = connection
    
    connection.connect(
      (data) => {
        if (data.type === 'connection_established') {
          setIsConnected(true)
          setConnectionError(null)
          console.log('实时连接已建立')
        } else {
          handleMessage(data)
        }
      },
      handleError
    )
  }, [enabled, handleMessage, handleError])
  
  // 断开连接
  const disconnect = useCallback(() => {
    if (connectionRef.current) {
      console.log('断开实时数据连接')
      connectionRef.current.disconnect()
      connectionRef.current = null
      setIsConnected(false)
      setConnectionError(null)
    }
  }, [])
  
  // 发送消息
  const sendMessage = useCallback((data: any) => {
    if (connectionRef.current && isConnected) {
      connectionRef.current.send(data)
    } else {
      console.warn('实时连接未建立，无法发送消息')
    }
  }, [isConnected])
  
  // 组件挂载时建立连接
  useEffect(() => {
    if (enabled) {
      connect()
    }
    
    return () => {
      disconnect()
    }
  }, [enabled, connect, disconnect])
  
  return {
    isConnected,
    connectionError,
    lastUpdate,
    connect,
    disconnect,
    sendMessage
  }
}

// 专门用于系统统计的实时数据Hook
export const useRealTimeStats = () => {
  const [stats, setStats] = useState({
    totalUsers: 1234,
    todayComments: 110,
    aiCalls: 2456,
    systemScore: 987
  })
  
  const [tokensData, setTokensData] = useState({
    hourly: [],
    daily: [],
    totalToday: 112960,
    totalWeek: 772260,
    costToday: 0.23,
    costWeek: 1.54
  })
  
  const [activities, setActivities] = useState([
    { user: '张老师', action: '生成了5条学生评语', time: '2分钟前', icon: '📝', color: '#51cf66' },
    { user: '李老师', action: '导入了新的学生数据', time: '15分钟前', icon: '📁', color: '#667eea' },
    { user: '王老师', action: '修改了AI配置参数', time: '1小时前', icon: '⚙️', color: '#ff6b6b' },
    { user: '赵老师', action: '导出了月度报告', time: '2小时前', icon: '📈', color: '#ffd43b' }
  ])
  
  // 处理用户活动更新
  const handleUserActivity = useCallback((data: any) => {
    setActivities(prev => [data, ...prev.slice(0, 9)]) // 保持最新10条记录
  }, [])
  
  // 处理tokens更新
  const handleTokensUpdate = useCallback((data: any) => {
    setTokensData(prev => ({
      ...prev,
      ...data
    }))
  }, [])
  
  // 处理评语生成事件
  const handleCommentGenerated = useCallback((data: any) => {
    setStats(prev => ({
      ...prev,
      todayComments: prev.todayComments + 1,
      aiCalls: prev.aiCalls + 1
    }))
  }, [])
  
  // 处理系统状态更新
  const handleSystemStatus = useCallback((data: any) => {
    setStats(prev => ({
      ...prev,
      ...data
    }))
  }, [])
  
  const { isConnected, connectionError, sendMessage } = useRealTimeData({
    onUserActivity: handleUserActivity,
    onTokensUpdate: handleTokensUpdate,
    onCommentGenerated: handleCommentGenerated,
    onSystemStatus: handleSystemStatus
  })
  
  return {
    stats,
    tokensData,
    activities,
    isConnected,
    connectionError,
    sendMessage
  }
}