import React, { useState, useMemo, useCallback } from 'react'
import { useRealTimeStats } from './hooks/useRealTimeData'
import { userApi, commentApi, tokensApi, handleApiError } from './services/miniProgramApi'
import ApiConnectionStatus from './components/ApiConnectionStatus'
import MiniProgramConnection from './components/MiniProgramConnection'
import ThemeToggle from './components/ThemeToggle'
import { useThemeStore } from './stores/themeStore'

const App: React.FC = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [currentPage, setCurrentPage] = useState('dashboard')
  const [currentTime, setCurrentTime] = useState(new Date())
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  
  // 主题状态
  const { isDark } = useThemeStore()
  
  // AI模型配置状态 - 移动到顶层
  const [selectedModel, setSelectedModel] = useState('doubao')
  const [apiConfigs, setApiConfigs] = useState({
    doubao: { apiKey: '', baseUrl: 'https://ark.cn-beijing.volces.com/api/v3', modelName: 'ep-20241123002057-k6rr4' },
    kimi: { apiKey: '', baseUrl: 'https://api.moonshot.cn/v1', modelName: 'moonshot-v1-8k' },
    deepseek: { apiKey: '', baseUrl: 'https://api.deepseek.com/v1', modelName: 'deepseek-chat' },
    qwen: { apiKey: '', baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1', modelName: 'qwen2-turbo' }
  })
  const [aiParams, setAiParams] = useState({
    temperature: 0.7,
    maxTokens: 2000,
    topP: 0.9,
    frequencyPenalty: 0.1
  })
  
  // Tokens图表视图状态
  const [tokensViewMode, setTokensViewMode] = useState('hour') // 'hour' | 'day'
  
  // 实时数据连接
  const { stats, tokensData, activities, isConnected, connectionError } = useRealTimeStats()

  // 更新时间 - 优化频率为30秒更新一次
  React.useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 30000)
    return () => clearInterval(timer)
  }, [])

  // 初始化主题
  React.useEffect(() => {
    document.documentElement.classList.toggle('dark', isDark)
    document.documentElement.setAttribute('data-theme', isDark ? 'dark' : 'light')
  }, [isDark])

  // 导航菜单项 - 使用useMemo优化
  const menuItems = useMemo(() => [
    { key: 'dashboard', icon: '📊', label: '数据大屏', description: '实时数据监控' },
    { key: 'ai-config', icon: '🤖', label: 'AI配置', description: '模型参数设置' },
    { key: 'data-management', icon: '📝', label: '数据管理', description: '教师用户管理' },
    { key: 'settings', icon: '⚙️', label: '系统设置', description: '个人与系统配置' }
  ], [])

  // 获取页面标题 - 使用useMemo优化
  const pageTitle = useMemo(() => {
    const page = menuItems.find(item => item.key === currentPage)
    return page ? page.label : '数据大屏'
  }, [currentPage, menuItems])

  // 页面切换函数 - 使用useCallback优化
  const handlePageChange = useCallback((pageKey: string) => {
    setCurrentPage(pageKey)
  }, [])

  // 侧边栏切换函数 - 使用useCallback优化  
  const toggleSidebar = useCallback(() => {
    setSidebarCollapsed(prev => !prev)
  }, [])

  // 通用按钮点击动效处理
  const addButtonClickEffect = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
    const button = e.currentTarget
    const originalTransform = button.style.transform
    button.style.transform = originalTransform.replace('scale(1)', 'scale(0.95)')
    setTimeout(() => {
      button.style.transform = originalTransform
    }, 150)
  }, [])

  // 通用按钮样式处理
  const getButtonStyle = useCallback((baseStyle: React.CSSProperties, hoverTransform = 'translateY(-1px) scale(1)') => ({
    ...baseStyle,
    transition: 'all 0.15s ease',
    transform: baseStyle.transform || 'translateY(0) scale(1)',
    cursor: 'pointer'
  }), [])

  const handleButtonHover = useCallback((e: React.MouseEvent<HTMLButtonElement>, hoverTransform = 'translateY(-1px) scale(1)', hoverShadow?: string) => {
    e.currentTarget.style.transform = hoverTransform
    if (hoverShadow) e.currentTarget.style.boxShadow = hoverShadow
  }, [])

  const handleButtonLeave = useCallback((e: React.MouseEvent<HTMLButtonElement>, normalTransform = 'translateY(0) scale(1)', normalShadow?: string) => {
    e.currentTarget.style.transform = normalTransform
    if (normalShadow !== undefined) e.currentTarget.style.boxShadow = normalShadow
  }, [])

  // 登录处理 - 使用useCallback优化
  const handleLogin = useCallback((e: React.FormEvent) => {
    e.preventDefault()
    const form = e.target as HTMLFormElement
    const formData = new FormData(form)
    const username = formData.get('username') as string
    const password = formData.get('password') as string
    
    if (username === 'admin' && password === 'admin123') {
      setIsLoggedIn(true)
    } else {
      alert('❌ 用户名或密码错误！\n请使用: admin / admin123')
    }
  }, [])

  // 登录界面
  if (!isLoggedIn) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* 动态背景装饰 */}
        <div style={{
          position: 'absolute',
          top: '-100px',
          left: '-100px',
          width: '300px',
          height: '300px',
          background: 'rgba(255,255,255,0.1)',
          borderRadius: '50%',
          animation: 'float 6s ease-in-out infinite'
        }}></div>
        <div style={{
          position: 'absolute',
          bottom: '-150px',
          right: '-150px',
          width: '400px',
          height: '400px',
          background: 'rgba(255,255,255,0.05)',
          borderRadius: '50%',
          animation: 'float 8s ease-in-out infinite reverse'
        }}></div>

        {/* 登录卡片 */}
        <div style={{
          background: 'rgba(255,255,255,0.95)',
          backdropFilter: 'blur(20px)',
          padding: '50px',
          borderRadius: '24px',
          boxShadow: '0 25px 50px rgba(0,0,0,0.2)',
          width: '100%',
          maxWidth: '450px',
          border: '1px solid rgba(255,255,255,0.2)',
          position: 'relative',
          zIndex: 10
        }}>
          {/* Logo区域 */}
          <div style={{ textAlign: 'center', marginBottom: '40px' }}>
            <div style={{
              width: '80px',
              height: '80px',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              borderRadius: '20px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 20px',
              boxShadow: '0 10px 30px rgba(102, 126, 234, 0.4)',
              fontSize: '32px'
            }}>
              🤖
            </div>
            <h1 style={{
              fontSize: '32px',
              fontWeight: '700',
              color: '#1a1a1a',
              marginBottom: '8px',
              fontFamily: '"SF Pro Display", -apple-system, sans-serif'
            }}>
              评语灵感君
            </h1>
            <p style={{
              color: '#666',
              fontSize: '18px',
              fontWeight: '500'
            }}>
              智能评语生成管理系统
            </p>
            <div style={{
              width: '60px',
              height: '4px',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              borderRadius: '2px',
              margin: '20px auto 0'
            }}></div>
          </div>

          {/* 登录表单 */}
          <form onSubmit={handleLogin}>
            <div style={{ marginBottom: '25px' }}>
              <input
                type="text"
                name="username"
                placeholder="请输入用户名"
                defaultValue="admin"
                style={{
                  width: '100%',
                  padding: '18px 20px',
                  border: '2px solid #f0f0f0',
                  borderRadius: '16px',
                  fontSize: '16px',
                  fontWeight: '500',
                  transition: 'all 0.3s ease',
                  background: '#fafafa'
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = '#667eea'
                  e.target.style.background = '#fff'
                  e.target.style.boxShadow = '0 0 0 4px rgba(102, 126, 234, 0.1)'
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#f0f0f0'
                  e.target.style.background = '#fafafa'
                  e.target.style.boxShadow = 'none'
                }}
              />
            </div>

            <div style={{ marginBottom: '35px' }}>
              <input
                type="password"
                name="password"
                placeholder="请输入密码"
                defaultValue="admin123"
                style={{
                  width: '100%',
                  padding: '18px 20px',
                  border: '2px solid #f0f0f0',
                  borderRadius: '16px',
                  fontSize: '16px',
                  fontWeight: '500',
                  transition: 'all 0.3s ease',
                  background: '#fafafa'
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = '#667eea'
                  e.target.style.background = '#fff'
                  e.target.style.boxShadow = '0 0 0 4px rgba(102, 126, 234, 0.1)'
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#f0f0f0'
                  e.target.style.background = '#fafafa'
                  e.target.style.boxShadow = 'none'
                }}
              />
            </div>

            <button
              type="submit"
              style={{
                width: '100%',
                padding: '18px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '16px',
                fontSize: '18px',
                fontWeight: '600',
                cursor: 'pointer',
                boxShadow: '0 8px 25px rgba(102, 126, 234, 0.4)',
                transition: 'all 0.15s ease',
                transform: 'translateY(0) scale(1)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px) scale(1)'
                e.currentTarget.style.boxShadow = '0 12px 35px rgba(102, 126, 234, 0.5)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0) scale(1)'
                e.currentTarget.style.boxShadow = '0 8px 25px rgba(102, 126, 234, 0.4)'
              }}
              onMouseDown={addButtonClickEffect}
            >
              🚀 立即登录
            </button>
          </form>

          {/* 提示信息 */}
          <div style={{
            textAlign: 'center',
            marginTop: '30px',
            padding: '20px',
            background: 'rgba(102, 126, 234, 0.05)',
            borderRadius: '12px',
            border: '1px solid rgba(102, 126, 234, 0.1)'
          }}>
            <p style={{ color: '#667eea', fontSize: '14px', fontWeight: '500', margin: '0 0 8px 0' }}>
              🔑 测试账号信息
            </p>
            <p style={{ color: '#999', fontSize: '13px', margin: '0' }}>
              用户名：admin  /  密码：admin123
            </p>
          </div>
        </div>

        <style>{`
          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
          }
          @keyframes slideUp {
            from { height: 0; opacity: 0; }
            to { opacity: 1; }
          }
        `}</style>
      </div>
    )
  }


  // 渲染页面内容
  const renderPageContent = () => {
    switch (currentPage) {
      case 'dashboard':
        return renderDashboard()
      case 'ai-config':
        return renderAIConfig()
      case 'data-management':
        return renderDataManagement()
      case 'settings':
        return renderSettings()
      default:
        return renderDashboard()
    }
  }

  // 数据大屏页面
  const renderDashboard = () => (
    <div>
      {/* 核心数据卡片 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(4, 1fr)',
        gap: '25px',
        marginBottom: '30px'
      }}>
        {[
          { title: '总用户数', value: stats.totalUsers.toLocaleString(), icon: '👥', color: '#667eea', change: '+12.5%' },
          { title: '今日生成评语', value: stats.todayComments.toString(), icon: '📝', color: '#51cf66', change: '+8.3%' },
          { title: 'AI调用次数', value: stats.aiCalls.toLocaleString(), icon: '🤖', color: '#ff6b6b', change: '+15.7%' },
          { title: '系统评分', value: `${stats.systemScore}分`, icon: '🏆', color: '#ffd43b', change: '+2.1%' }
        ].map((stat, index) => (
          <div
            key={index}
            className="theme-card theme-shadow"
            style={{
              backdropFilter: 'blur(20px)',
              padding: '30px',
              borderRadius: '20px',
              transition: 'all 0.3s ease',
              cursor: 'pointer',
              position: 'relative',
              overflow: 'hidden'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-5px)'
              e.currentTarget.style.boxShadow = '0 15px 45px rgba(0,0,0,0.15)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)'
              e.currentTarget.style.boxShadow = '0 8px 32px rgba(0,0,0,0.1)'
            }}
            onClick={() => alert(`${stat.title}: ${stat.value}`)}
            onMouseDown={(e) => {
              const div = e.currentTarget
              div.style.transform = 'translateY(-5px) scale(0.98)'
              setTimeout(() => {
                div.style.transform = 'translateY(-5px) scale(1)'
              }, 150)
            }}
          >
            <div style={{
              position: 'absolute',
              top: '-50px',
              right: '-50px',
              width: '120px',
              height: '120px',
              background: `${stat.color}15`,
              borderRadius: '50%'
            }}></div>
            
            <div style={{ position: 'relative', zIndex: 2 }}>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '15px' }}>
                <div style={{
                  width: '50px',
                  height: '50px',
                  background: stat.color,
                  borderRadius: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '20px'
                }}>
                  {stat.icon}
                </div>
                <div style={{
                  background: '#51cf66',
                  color: 'white',
                  padding: '4px 10px',
                  borderRadius: '12px',
                  fontSize: '12px',
                  fontWeight: '600'
                }}>
                  {stat.change}
                </div>
              </div>
              <h3 className="theme-text-primary" style={{
                fontSize: '36px',
                fontWeight: '700',
                margin: '0 0 8px 0'
              }}>
                {stat.value}
              </h3>
              <p className="theme-text-secondary" style={{
                fontSize: '16px',
                fontWeight: '500',
                margin: 0
              }}>
                {stat.title}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* 数据可视化图表 */}
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))', gap: '25px', marginBottom: '30px' }}>
        {/* 用户增长趋势图 */}
        <div 
          className="theme-card theme-shadow"
          style={{
            backdropFilter: 'blur(20px)',
            padding: '30px',
            borderRadius: '20px'
          }}>
          <h4 className="theme-text-primary" style={{ margin: '0 0 20px 0', fontSize: '18px', fontWeight: '700', display: 'flex', alignItems: 'center', gap: '10px' }}>
            <span style={{ background: '#667eea', color: 'white', padding: '5px 10px', borderRadius: '8px', fontSize: '14px' }}>📈</span>
            用户增长趋势
          </h4>
          <div style={{ height: '200px', background: 'linear-gradient(135deg, #667eea20 0%, #764ba220 100%)', borderRadius: '12px', position: 'relative', overflow: 'hidden' }}>
            <svg width="100%" height="100%" style={{ position: 'absolute' }}>
              <defs>
                <linearGradient id="gradient1" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" style={{ stopColor: '#667eea', stopOpacity: 0.3 }} />
                  <stop offset="100%" style={{ stopColor: '#667eea', stopOpacity: 0.05 }} />
                </linearGradient>
              </defs>
              <path d="M50,150 Q150,120 250,100 T450,80" stroke="#667eea" strokeWidth="3" fill="none" />
              <path d="M50,150 Q150,120 250,100 T450,80 L450,200 L50,200 Z" fill="url(#gradient1)" />
              {[50, 150, 250, 350, 450].map((x, i) => (
                <circle key={i} cx={x} cy={150 - i * 15} r="4" fill="#667eea" />
              ))}
            </svg>
            <div style={{ position: 'absolute', bottom: '10px', right: '15px', fontSize: '12px', color: '#667eea', fontWeight: '600' }}>
              最近7天数据
            </div>
          </div>
        </div>

        {/* Tokens消耗统计图 */}
        <div 
          className="theme-card theme-shadow"
          style={{
            backdropFilter: 'blur(20px)',
            padding: '30px',
            borderRadius: '20px'
          }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '20px' }}>
            <h4 className="theme-text-primary" style={{ margin: 0, fontSize: '18px', fontWeight: '700', display: 'flex', alignItems: 'center', gap: '10px' }}>
              <span style={{ background: '#51cf66', color: 'white', padding: '5px 10px', borderRadius: '8px', fontSize: '14px' }}>📊</span>
              Tokens消耗统计
            </h4>
            <div style={{ display: 'flex', gap: '10px' }}>
              <button 
                style={{
                  background: tokensViewMode === 'day' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'rgba(102, 126, 234, 0.1)',
                  color: tokensViewMode === 'day' ? 'white' : '#667eea',
                  border: tokensViewMode === 'day' ? 'none' : '1px solid rgba(102, 126, 234, 0.3)',
                  padding: '6px 12px',
                  borderRadius: '6px',
                  fontSize: '12px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  transform: 'translateY(0)'
                }}
                onClick={() => setTokensViewMode('day')}
                onMouseEnter={(e) => handleButtonHover(e, 'translateY(-1px) scale(1)')}
                onMouseLeave={(e) => handleButtonLeave(e, 'translateY(0) scale(1)')}
                onMouseDown={addButtonClickEffect}
              >
                日视图
              </button>
              <button 
                style={{
                  background: tokensViewMode === 'hour' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'rgba(102, 126, 234, 0.1)',
                  color: tokensViewMode === 'hour' ? 'white' : '#667eea',
                  border: tokensViewMode === 'hour' ? 'none' : '1px solid rgba(102, 126, 234, 0.3)',
                  padding: '6px 12px',
                  borderRadius: '6px',
                  fontSize: '12px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  transform: 'translateY(0)'
                }}
                onClick={() => setTokensViewMode('hour')}
                onMouseEnter={(e) => handleButtonHover(e, 'translateY(-1px) scale(1)')}
                onMouseLeave={(e) => handleButtonLeave(e, 'translateY(0) scale(1)')}
                onMouseDown={addButtonClickEffect}
              >
                小时视图
              </button>
            </div>
          </div>
          <div style={{ height: '200px', display: 'flex', alignItems: 'end', gap: '6px', padding: '10px 0' }}>
            {(tokensViewMode === 'hour' ? [
              { tokens: 15240, label: '今日', time: '00:00', color: '#667eea' },
              { tokens: 18320, label: '', time: '04:00', color: '#51cf66' },
              { tokens: 12150, label: '', time: '08:00', color: '#ffd43b' },
              { tokens: 22450, label: '', time: '12:00', color: '#ff6b6b' },
              { tokens: 19680, label: '', time: '16:00', color: '#667eea' },
              { tokens: 25120, label: '当前', time: '20:00', color: '#51cf66' }
            ] : [
              { tokens: 89240, label: '', time: '周一', color: '#667eea' },
              { tokens: 95320, label: '', time: '周二', color: '#51cf66' },
              { tokens: 78150, label: '', time: '周三', color: '#ffd43b' },
              { tokens: 112450, label: '', time: '周四', color: '#ff6b6b' },
              { tokens: 98680, label: '', time: '周五', color: '#667eea' },
              { tokens: 156120, label: '今日', time: '周六', color: '#51cf66' },
              { tokens: 142300, label: '', time: '周日', color: '#ffd43b' }
            ]).map((data, i) => {
              const maxTokens = 30000
              const height = `${(data.tokens / maxTokens) * 100}%`
              return (
                <div key={i} style={{ flex: 1, display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '8px' }}>
                  <div 
                    style={{
                      width: '100%',
                      height: height,
                      background: `linear-gradient(180deg, ${data.color} 0%, ${data.color}80 100%)`,
                      borderRadius: '4px 4px 0 0',
                      position: 'relative',
                      animation: `slideUp 1.2s ease ${i * 0.1}s both`,
                      cursor: 'pointer'
                    }}
                    title={`${data.tokens.toLocaleString()} tokens`}
                    onClick={() => alert(`${data.time}: ${data.tokens.toLocaleString()} tokens`)}
                    onMouseDown={addButtonClickEffect}
                  >
                    <div style={{
                      position: 'absolute',
                      top: '-25px',
                      left: '50%',
                      transform: 'translateX(-50%)',
                      fontSize: '10px',
                      fontWeight: '600',
                      color: data.color,
                      whiteSpace: 'nowrap'
                    }}>
                      {(data.tokens / 1000).toFixed(1)}K
                    </div>
                  </div>
                  <span style={{ fontSize: '10px', color: '#666', fontWeight: '600', textAlign: 'center' }}>
                    {data.time}
                  </span>
                  {data.label && (
                    <span style={{ fontSize: '9px', color: data.color, fontWeight: '700' }}>
                      {data.label}
                    </span>
                  )}
                </div>
              )
            })}
          </div>
          <div style={{ marginTop: '15px', padding: '15px', background: 'rgba(102, 126, 234, 0.05)', borderRadius: '12px' }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                <span style={{ fontSize: '14px', color: '#667eea', fontWeight: '600' }}>
                  📊 {tokensViewMode === 'hour' ? '今日总消耗' : '本周总消耗'}:
                </span>
                <span style={{ fontSize: '16px', fontWeight: '700', color: '#1a1a1a' }}>
                  {(tokensViewMode === 'hour' ? tokensData.totalToday : tokensData.totalWeek).toLocaleString()} tokens
                </span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                <span style={{ fontSize: '14px', color: '#51cf66', fontWeight: '600' }}>💰 估算成本:</span>
                <span style={{ fontSize: '16px', fontWeight: '700', color: '#1a1a1a' }}>
                  ￥{(tokensViewMode === 'hour' ? tokensData.costToday : tokensData.costWeek).toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 活动日志 */}
      <div 
        className="theme-card theme-shadow"
        style={{
          backdropFilter: 'blur(20px)',
          padding: '30px',
          borderRadius: '20px'
        }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '25px' }}>
          <div style={{
            width: '40px',
            height: '40px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '10px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: '15px',
            fontSize: '16px'
          }}>
            📊
          </div>
          <div>
            <h3 className="theme-text-primary" style={{ margin: '0 0 5px 0', fontSize: '20px', fontWeight: '700' }}>
              实时系统动态
            </h3>
            <p className="theme-text-secondary" style={{ margin: 0, fontSize: '14px' }}>
              最新的系统活动和用户操作记录
            </p>
          </div>
        </div>

        <div style={{ display: 'grid', gap: '15px' }}>
          {activities.map((activity, index) => (
            <div
              key={index}
              className="theme-card-secondary theme-border"
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '15px',
                borderRadius: '12px',
                border: '1px solid',
                transition: 'all 0.3s ease',
                cursor: 'pointer'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateX(5px)'
                e.currentTarget.style.opacity = '0.8'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateX(0)'
                e.currentTarget.style.opacity = '1'
              }}
              onClick={() => alert(`${activity.user} - ${activity.action} (${activity.time})`)}
              onMouseDown={(e) => {
                const div = e.currentTarget
                div.style.transform = 'translateX(5px) scale(0.98)'
                setTimeout(() => {
                  div.style.transform = 'translateX(5px) scale(1)'
                }, 150)
              }}
            >
              <div style={{
                width: '40px',
                height: '40px',
                background: activity.color,
                borderRadius: '10px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: '15px',
                fontSize: '16px'
              }}>
                {activity.icon}
              </div>
              <div style={{ flex: 1 }}>
                <p className="theme-text-primary" style={{
                  margin: '0 0 4px 0',
                  fontSize: '16px',
                  fontWeight: '600'
                }}>
                  {activity.user}
                </p>
                <p className="theme-text-secondary" style={{
                  margin: 0,
                  fontSize: '14px'
                }}>
                  {activity.action}
                </p>
              </div>
              <div className="theme-text-tertiary" style={{
                fontSize: '12px',
                fontWeight: '500'
              }}>
                {activity.time}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  // AI配置页面
  const renderAIConfig = () => (
    <div style={{ display: 'grid', gap: '25px' }}>
      {/* 模型选择和配置 */}
      <div 
        className="theme-card theme-shadow"
        style={{
          backdropFilter: 'blur(20px)',
          padding: '30px',
          borderRadius: '20px'
        }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '25px' }}>
          <div style={{
            width: '50px',
            height: '50px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: '15px',
            fontSize: '20px'
          }}>
            🤖
          </div>
          <div>
            <h3 className="theme-text-primary" style={{ margin: '0 0 5px 0', fontSize: '24px', fontWeight: '700' }}>
              AI模型配置
            </h3>
            <p className="theme-text-secondary" style={{ margin: 0, fontSize: '14px' }}>
              配置不同AI模型的API接入参数
            </p>
          </div>
        </div>

        {/* 模型选择器 */}
        <div style={{ marginBottom: '30px' }}>
          <label className="theme-text-primary" style={{ display: 'block', fontSize: '16px', fontWeight: '700', marginBottom: '15px' }}>
            🎯 选择AI模型
          </label>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
            {[
              { key: 'doubao', name: '豆包模型', provider: '字节跳动', icon: '🍿', color: '#51cf66' },
              { key: 'kimi', name: 'Kimi', provider: 'Moonshot AI', icon: '🌙', color: '#667eea' },
              { key: 'deepseek', name: 'DeepSeek', provider: 'DeepSeek', icon: '🔍', color: '#ff6b6b' },
              { key: 'qwen', name: 'Qwen2', provider: '阿里云', icon: '☁️', color: '#ffd43b' }
            ].map((model) => (
              <div
                key={model.key}
                onClick={() => setSelectedModel(model.key)}
                onMouseDown={addButtonClickEffect}
                className={selectedModel === model.key ? '' : 'theme-card-secondary'}
                style={{
                  background: selectedModel === model.key ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : undefined,
                  color: selectedModel === model.key ? 'white' : 'var(--text-primary)',
                  padding: '20px',
                  borderRadius: '16px',
                  border: selectedModel === model.key ? '2px solid #667eea' : '2px solid rgba(0,0,0,0.05)',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  textAlign: 'center',
                  position: 'relative',
                  overflow: 'hidden'
                }}
                onMouseEnter={(e) => {
                  if (selectedModel !== model.key) {
                    e.currentTarget.style.background = 'rgba(102, 126, 234, 0.1)'
                    e.currentTarget.style.borderColor = 'rgba(102, 126, 234, 0.3)'
                  }
                }}
                onMouseLeave={(e) => {
                  if (selectedModel !== model.key) {
                    e.currentTarget.style.background = 'rgba(255,255,255,0.7)'
                    e.currentTarget.style.borderColor = 'rgba(0,0,0,0.05)'
                  }
                }}
                onMouseDown={(e) => {
                  const div = e.currentTarget
                  div.style.transform = 'scale(0.95)'
                  setTimeout(() => {
                    div.style.transform = 'scale(1)'
                  }, 150)
                }}
              >
                <div style={{ fontSize: '24px', marginBottom: '10px' }}>{model.icon}</div>
                <h4 style={{ margin: '0 0 5px 0', fontSize: '16px', fontWeight: '700' }}>{model.name}</h4>
                <p style={{ margin: 0, fontSize: '12px', opacity: 0.8 }}>{model.provider}</p>
                {selectedModel === model.key && (
                  <div style={{
                    position: 'absolute',
                    top: '10px',
                    right: '10px',
                    background: 'rgba(255,255,255,0.2)',
                    borderRadius: '50%',
                    width: '20px',
                    height: '20px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '12px'
                  }}>
                    ✓
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* API配置表单 */}
        <div 
          className="theme-card-secondary theme-border"
          style={{
            padding: '25px',
            borderRadius: '16px',
            border: '1px solid'
          }}>
          <h4 className="theme-text-primary" style={{ margin: '0 0 20px 0', fontSize: '18px', fontWeight: '700' }}>
            🔑 {selectedModel === 'doubao' ? '豆包' : selectedModel === 'kimi' ? 'Kimi' : selectedModel === 'deepseek' ? 'DeepSeek' : 'Qwen2'} API配置
          </h4>
          
          <div style={{ display: 'grid', gap: '20px' }}>
            <div>
              <label className="theme-text-primary" style={{ display: 'block', fontSize: '14px', fontWeight: '600', marginBottom: '8px' }}>
                API Key *
              </label>
              <input
                type="password"
                placeholder="请输入API Key"
                value={apiConfigs[selectedModel].apiKey}
                onChange={(e) => setApiConfigs(prev => ({
                  ...prev,
                  [selectedModel]: { ...prev[selectedModel], apiKey: e.target.value }
                }))}
                className="theme-input"
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  border: '2px solid',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontFamily: 'monospace'
                }}
              />
            </div>
            
            
            <div>
              <label className="theme-text-primary" style={{ display: 'block', fontSize: '14px', fontWeight: '600', marginBottom: '8px' }}>
                Base URL
              </label>
              <input
                type="url"
                placeholder="API基础地址"
                value={apiConfigs[selectedModel].baseUrl}
                onChange={(e) => setApiConfigs(prev => ({
                  ...prev,
                  [selectedModel]: { ...prev[selectedModel], baseUrl: e.target.value }
                }))}
                className="theme-input"
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  border: '2px solid',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontFamily: 'monospace'
                }}
              />
            </div>
            
            <div>
              <label className="theme-text-primary" style={{ display: 'block', fontSize: '14px', fontWeight: '600', marginBottom: '8px' }}>
                模型名称
              </label>
              <input
                type="text"
                placeholder="模型名称"
                value={apiConfigs[selectedModel].modelName}
                onChange={(e) => setApiConfigs(prev => ({
                  ...prev,
                  [selectedModel]: { ...prev[selectedModel], modelName: e.target.value }
                }))}
                className="theme-input"
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  border: '2px solid',
                  borderRadius: '8px',
                  fontSize: '14px'
                }}
              />
            </div>
            
            {/* 测试连接按钮 */}
            <div style={{ display: 'flex', gap: '10px', marginTop: '10px' }}>
              <button 
                onClick={() => alert('正在测试API连接...')}
                onMouseDown={addButtonClickEffect}
                style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                  border: 'none',
                  padding: '10px 20px',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.15s ease',
                  transform: 'scale(1)'
                }}
              >
                🔍 测试连接
              </button>
              <button 
                onClick={() => {
                  console.log('保存配置:', apiConfigs[selectedModel])
                  alert('配置已保存!')
                }}
                onMouseDown={addButtonClickEffect}
                style={{
                  background: 'linear-gradient(135deg, #51cf66 0%, #40c057 100%)',
                  color: 'white',
                  border: 'none',
                  padding: '10px 20px',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.15s ease',
                  transform: 'scale(1)'
                }}
              >
                💾 保存配置
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* AI参数配置 */}
      <div 
        className="theme-card theme-shadow"
        style={{
          backdropFilter: 'blur(20px)',
          padding: '30px',
          borderRadius: '20px'
        }}>
        <h3 className="theme-text-primary" style={{ margin: '0 0 20px 0', fontSize: '20px', fontWeight: '700' }}>
          🎛️ 生成参数配置
        </h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>
          {[
            { key: 'temperature', label: '温度参数 (Temperature)', range: '0.0 - 1.0', description: '控制输出的创造性和随机性' },
            { key: 'maxTokens', label: '最大Token数', range: '1 - 4000', description: '生成文本的最大长度' },
            { key: 'topP', label: '核采样 (Top P)', range: '0.0 - 1.0', description: '控制词汇选择的多样性' },
            { key: 'frequencyPenalty', label: '频率惩罚', range: '0.0 - 2.0', description: '减少重复内容的出现' }
          ].map((param) => (
            <div 
              key={param.key} 
              className="theme-card-secondary theme-border"
              style={{
                padding: '20px',
                borderRadius: '16px',
                border: '1px solid'
              }}>
              <label className="theme-text-primary" style={{ display: 'block', fontSize: '14px', fontWeight: '600', marginBottom: '8px' }}>
                {param.label}
              </label>
              <div style={{ display: 'flex', alignItems: 'center', gap: '15px', marginBottom: '10px' }}>
                <input
                  type="range"
                  min={param.key === 'maxTokens' ? '1' : '0'}
                  max={param.key === 'maxTokens' ? '4000' : param.key === 'frequencyPenalty' ? '2' : '1'}
                  step={param.key === 'maxTokens' ? '50' : '0.1'}
                  value={aiParams[param.key]}
                  onChange={(e) => setAiParams(prev => ({ ...prev, [param.key]: parseFloat(e.target.value) }))}
                  style={{ flex: 1 }}
                />
                <input
                  type="number"
                  value={aiParams[param.key]}
                  onChange={(e) => setAiParams(prev => ({ ...prev, [param.key]: parseFloat(e.target.value) }))}
                  min={param.key === 'maxTokens' ? '1' : '0'}
                  max={param.key === 'maxTokens' ? '4000' : param.key === 'frequencyPenalty' ? '2' : '1'}
                  step={param.key === 'maxTokens' ? '50' : '0.1'}
                  className="theme-input"
                  style={{
                    width: '80px',
                    padding: '6px 8px',
                    border: '1px solid',
                    borderRadius: '6px',
                    fontSize: '14px',
                    textAlign: 'center'
                  }}
                />
              </div>
              <p className="theme-text-tertiary" style={{ margin: '0 0 5px 0', fontSize: '11px' }}>
                范围: {param.range}
              </p>
              <p className="theme-text-secondary" style={{ margin: 0, fontSize: '12px' }}>
                {param.description}
              </p>
            </div>
          ))}
        </div>
        
        <div style={{ marginTop: '25px', display: 'flex', gap: '15px' }}>
          <button 
            onClick={() => {
              console.log('保存AI参数:', aiParams)
              alert('参数配置已保存!')
            }}
            onMouseDown={addButtonClickEffect}
            style={{
              background: 'linear-gradient(135deg, #51cf66 0%, #40c057 100%)',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '12px',
              fontSize: '16px',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.15s ease',
              transform: 'scale(1)'
            }}
          >
            💾 保存参数
          </button>
          <button 
            onClick={() => {
              setAiParams({ temperature: 0.7, maxTokens: 2000, topP: 0.9, frequencyPenalty: 0.1 })
              alert('已重置为默认值')
            }}
            onMouseDown={addButtonClickEffect}
            style={{
              background: 'rgba(102, 126, 234, 0.1)',
              color: '#667eea',
              border: '1px solid rgba(102, 126, 234, 0.3)',
              padding: '12px 24px',
              borderRadius: '12px',
              fontSize: '16px',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.15s ease',
              transform: 'scale(1)'
            }}
          >
            🔄 重置默认
          </button>
        </div>
      </div>
    </div>
  )

  // 数据管理页面
  const renderDataManagement = () => (
    <div style={{ display: 'grid', gap: '25px' }}>
      {/* 学生管理 */}
      <div 
        className="theme-card theme-shadow"
        style={{
          backdropFilter: 'blur(20px)',
          padding: '30px',
          borderRadius: '20px'
        }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '25px' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{
              width: '50px',
              height: '50px',
              background: 'linear-gradient(135deg, #51cf66 0%, #40c057 100%)',
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '15px',
              fontSize: '20px'
            }}>
              👥
            </div>
            <div>
              <h3 className="theme-text-primary" style={{ margin: '0 0 5px 0', fontSize: '24px', fontWeight: '700' }}>
                教师用户管理
              </h3>
              <p className="theme-text-secondary" style={{ margin: 0, fontSize: '14px' }}>
                管理教师用户信息和使用记录
              </p>
            </div>
          </div>
          <div style={{ display: 'flex', gap: '10px' }}>
            <button 
              style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                border: 'none',
                padding: '10px 20px',
                borderRadius: '10px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.15s ease',
                transform: 'scale(1)'
              }}
              onClick={() => {
                const input = document.createElement('input')
                input.type = 'file'
                input.accept = '.xlsx,.csv'
                input.onchange = (e) => {
                  const file = (e.target as HTMLInputElement).files?.[0]
                  if (file) {
                    console.log('导入文件:', file.name)
                    alert(`正在处理文件: ${file.name}...`)
                  }
                }
                input.click()
              }}
              onMouseDown={addButtonClickEffect}
            >
              📥 导入教师
            </button>
            <button 
              style={{
                background: 'rgba(102, 126, 234, 0.1)',
                color: '#667eea',
                border: '1px solid rgba(102, 126, 234, 0.2)',
                padding: '10px 20px',
                borderRadius: '10px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.15s ease',
                transform: 'scale(1)'
              }}
              onClick={() => {
                const data = '教师姓名,学校,联系方式,使用次数,限额\n王老师,实验小学,138****1234,18,20\n李老师,育才小学,139****5678,15,20'
                const blob = new Blob([data], { type: 'text/csv;charset=utf-8;' })
                const link = document.createElement('a')
                link.href = URL.createObjectURL(blob)
                link.download = `教师数据_${new Date().toISOString().split('T')[0]}.csv`
                link.click()
                alert('数据已导出为 CSV 文件!')
              }}
              onMouseDown={addButtonClickEffect}
            >
              📤 导出数据
            </button>
          </div>
        </div>

        <div style={{ overflowX: 'auto' }}>
          <div style={{ display: 'grid', gridTemplateColumns: '2fr 2fr 1.2fr 1.5fr', gap: '15px', minWidth: '600px' }}>
            {/* 表头 */}
            <div className="theme-card-secondary" style={{ padding: '15px', borderRadius: '12px', fontSize: '14px', fontWeight: '700', color: '#667eea' }}>
              微信名称
            </div>
            <div className="theme-card-secondary" style={{ padding: '15px', borderRadius: '12px', fontSize: '14px', fontWeight: '700', color: '#667eea' }}>
              教师昵称
            </div>
            <div className="theme-card-secondary" style={{ padding: '15px', borderRadius: '12px', fontSize: '14px', fontWeight: '700', color: '#667eea' }}>
              使用次数/限额
            </div>
            <div className="theme-card-secondary" style={{ padding: '15px', borderRadius: '12px', fontSize: '14px', fontWeight: '700', color: '#667eea' }}>
              操作
            </div>

            {/* 数据行 */}
            {[
              { wechatName: '小明老师', teacherName: '王小明', used: 18, limit: 20 },
              { wechatName: 'Lisa_teacher', teacherName: '李丽莎', used: 15, limit: 20 },
              { wechatName: '张老师123', teacherName: '张建国', used: 8, limit: 30 },
              { wechatName: '陈教师', teacherName: '陈美丽', used: 20, limit: 20 }
            ].map((teacher, index) => (
              <React.Fragment key={index}>
                <div className="theme-card-secondary theme-text-primary" style={{ padding: '15px', borderRadius: '12px', fontSize: '14px', fontWeight: '600' }}>
                  {teacher.wechatName}
                </div>
                <div className="theme-card-secondary theme-text-secondary" style={{ padding: '15px', borderRadius: '12px', fontSize: '14px' }}>
                  {teacher.teacherName}
                </div>
                <div className="theme-card-secondary theme-text-secondary" style={{ padding: '15px', borderRadius: '12px', fontSize: '14px', display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <span style={{ 
                    fontSize: '16px', 
                    fontWeight: '700',
                    color: teacher.used >= teacher.limit ? '#ff6b6b' : teacher.used >= teacher.limit * 0.8 ? '#ffd43b' : '#51cf66'
                  }}>
                    {teacher.used}
                  </span>
                  <span style={{ color: '#666', fontSize: '12px' }}>/ {teacher.limit}</span>
                  <div style={{
                    width: '40px',
                    height: '6px',
                    background: 'rgba(0,0,0,0.1)',
                    borderRadius: '3px',
                    overflow: 'hidden'
                  }}>
                    <div style={{
                      width: `${Math.min((teacher.used / teacher.limit) * 100, 100)}%`,
                      height: '100%',
                      background: teacher.used >= teacher.limit ? '#ff6b6b' : teacher.used >= teacher.limit * 0.8 ? '#ffd43b' : '#51cf66',
                      borderRadius: '3px',
                      transition: 'all 0.3s ease'
                    }}></div>
                  </div>
                </div>
                <div className="theme-card-secondary" style={{ padding: '15px', borderRadius: '12px', display: 'flex', gap: '5px', alignItems: 'center' }}>
                  <button 
                    style={{
                      background: '#51cf66',
                      color: 'white',
                      border: 'none',
                      padding: '5px 10px',
                      borderRadius: '6px',
                      fontSize: '11px',
                      cursor: 'pointer',
                      fontWeight: '600',
                      transition: 'all 0.15s ease',
                      transform: 'scale(1)'
                    }}
                    onClick={() => {
                      const newLimit = prompt(`为 ${teacher.teacherName} 设置新的月度使用限额：`, teacher.limit.toString())
                      if (newLimit && !isNaN(parseInt(newLimit))) {
                        console.log(`设置 ${teacher.teacherName} 的限额为 ${newLimit} 次`)
                        alert(`已将 ${teacher.teacherName} 的月度使用限额设置为 ${newLimit} 次`)
                      }
                    }}
                    onMouseDown={addButtonClickEffect}
                  >
                    调额
                  </button>
                  <button 
                    style={{
                      background: '#667eea',
                      color: 'white',
                      border: 'none',
                      padding: '5px 10px',
                      borderRadius: '6px',
                      fontSize: '11px',
                      cursor: 'pointer',
                      fontWeight: '600',
                      transition: 'all 0.15s ease',
                      transform: 'scale(1)'
                    }}
                    onClick={() => {
                      if (confirm(`确认重置 ${teacher.teacherName} 的月度使用次数吗？`)) {
                        console.log(`重置 ${teacher.teacherName} 的使用次数`)
                        alert(`已重置 ${teacher.teacherName} 的月度使用次数`)
                      }
                    }}
                    onMouseDown={addButtonClickEffect}
                  >
                    重置
                  </button>
                  <button 
                    style={{
                      background: 'rgba(102, 126, 234, 0.1)',
                      color: '#667eea',
                      border: '1px solid rgba(102, 126, 234, 0.2)',
                      padding: '5px 10px',
                      borderRadius: '6px',
                      fontSize: '11px',
                      cursor: 'pointer',
                      fontWeight: '600',
                      transition: 'all 0.15s ease',
                      transform: 'scale(1)'
                    }}
                    onClick={() => {
                      alert(`${teacher.teacherName} 的详细信息：\n微信名称：${teacher.wechatName}\n教师昵称：${teacher.teacherName}\n已使用：${teacher.used}/${teacher.limit} 次\n状态：${teacher.used >= teacher.limit ? '已超限' : '正常'}`)
                    }}
                    onMouseDown={addButtonClickEffect}
                  >
                    详情
                  </button>
                </div>
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>
    </div>
  )

  // 系统设置页面
  const renderSettings = () => (
    <div style={{ display: 'grid', gap: '25px' }}>
      {/* 个人信息 */}
      <div 
        className="theme-card theme-shadow"
        style={{
          backdropFilter: 'blur(20px)',
          padding: '30px',
          borderRadius: '20px'
        }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '25px' }}>
          <div style={{
            width: '50px',
            height: '50px',
            background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)',
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: '15px',
            fontSize: '20px'
          }}>
            👤
          </div>
          <div>
            <h3 className="theme-text-primary" style={{ margin: '0 0 5px 0', fontSize: '24px', fontWeight: '700' }}>
              个人信息
            </h3>
            <p className="theme-text-secondary" style={{ margin: 0, fontSize: '14px' }}>
              管理个人账户信息和偏好设置
            </p>
          </div>
        </div>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>
          {[
            { label: '姓名', value: '系统管理员', type: 'text' },
            { label: '用户名', value: 'admin', type: 'text', disabled: true },
            { label: '邮箱', value: '<EMAIL>', type: 'email' },
            { label: '手机号', value: '', type: 'tel' }
          ].map((field, index) => (
            <div key={index}>
              <label className="theme-text-primary" style={{ display: 'block', fontSize: '14px', fontWeight: '600', marginBottom: '8px' }}>
                {field.label}
              </label>
              <input
                type={field.type}
                defaultValue={field.value}
                disabled={field.disabled}
                className="theme-input"
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '2px solid',
                  borderRadius: '8px',
                  fontSize: '16px',
                  opacity: field.disabled ? 0.6 : 1
                }}
              />
            </div>
          ))}
        </div>
        <button 
          style={{
            background: 'linear-gradient(135deg, #51cf66 0%, #40c057 100%)',
            color: 'white',
            border: 'none',
            padding: '12px 24px',
            borderRadius: '12px',
            fontSize: '16px',
            fontWeight: '600',
            cursor: 'pointer',
            marginTop: '20px',
            transition: 'all 0.15s ease',
            transform: 'scale(1)'
          }}
          onClick={() => alert('个人信息已保存!')}
          onMouseDown={addButtonClickEffect}
        >
          💾 保存个人信息
        </button>
      </div>

      {/* 系统配置 */}
      <div 
        className="theme-card theme-shadow"
        style={{
          backdropFilter: 'blur(20px)',
          padding: '30px',
          borderRadius: '20px'
        }}>
        <h3 className="theme-text-primary" style={{ margin: '0 0 20px 0', fontSize: '20px', fontWeight: '700' }}>
          ⚙️ 系统配置
        </h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>
          {[
            { label: '系统名称', value: '评语灵感君管理后台', type: 'text' },
            { label: '系统版本', value: 'v3.0.0', type: 'text', disabled: true },
            { label: '最大用户数', value: '1000', type: 'number' },
            { label: '会话超时(分钟)', value: '30', type: 'number' }
          ].map((field, index) => (
            <div key={index}>
              <label className="theme-text-primary" style={{ display: 'block', fontSize: '14px', fontWeight: '600', marginBottom: '8px' }}>
                {field.label}
              </label>
              <input
                type={field.type}
                defaultValue={field.value}
                disabled={field.disabled}
                className="theme-input"
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '2px solid',
                  borderRadius: '8px',
                  fontSize: '16px',
                  opacity: field.disabled ? 0.6 : 1
                }}
              />
            </div>
          ))}
        </div>
        <button 
          style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            padding: '12px 24px',
            borderRadius: '12px',
            fontSize: '16px',
            fontWeight: '600',
            cursor: 'pointer',
            marginTop: '20px',
            transition: 'all 0.15s ease',
            transform: 'scale(1)'
          }}
          onClick={() => alert('系统配置已保存!')}
          onMouseDown={addButtonClickEffect}
        >
          💾 保存系统配置
        </button>
      </div>
      
      {/* 小程序连接配置 */}
      <MiniProgramConnection onConfigSave={(config) => console.log('保存配置:', config)} />
    </div>
  )

  // 主应用界面
  return (
    <div 
      className="theme-container theme-gradient-light"
      style={{
        minHeight: '100vh',
        fontFamily: '"SF Pro Display", -apple-system, sans-serif',
        display: 'flex'
      }}>
      {/* 侧边栏 */}
      <div 
        className="theme-card"
        style={{
          width: sidebarCollapsed ? '80px' : '240px',
          backdropFilter: 'blur(20px)',
          transition: 'all 0.3s ease',
          position: 'fixed',
          height: '100vh',
          zIndex: 1000,
          overflowY: 'auto',
          borderRight: '1px solid var(--border-color)'
        }}>
        {/* Logo区域 */}
        <div 
          className="theme-border"
          style={{
            padding: '25px',
            borderBottom: '1px solid',
            textAlign: 'center'
          }}>
          {!sidebarCollapsed && (
            <div>
              <h3 className="theme-text-primary" style={{ margin: '0 0 8px 0', fontSize: '26px', fontWeight: '900', letterSpacing: '0.8px' }}>
                评语灵感君
              </h3>
              <p className="theme-text-secondary" style={{ margin: 0, fontSize: '14px', fontWeight: '600' }}>
                智能管理系统
              </p>
            </div>
          )}
        </div>

        {/* 导航菜单 */}
        <div style={{ padding: '20px 15px' }}>
          {menuItems.map((item) => (
            <div
              key={item.key}
              onClick={() => handlePageChange(item.key)}
              onMouseDown={addButtonClickEffect}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '15px',
                padding: '15px',
                marginBottom: '8px',
                borderRadius: '12px',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                background: currentPage === item.key 
                  ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' 
                  : 'transparent',
                color: currentPage === item.key ? 'white' : 'var(--text-primary)',
                transform: currentPage === item.key ? 'translateX(5px)' : 'translateX(0)'
              }}
              onMouseEnter={(e) => {
                if (currentPage !== item.key) {
                  e.currentTarget.style.background = 'rgba(102, 126, 234, 0.1)'
                }
              }}
              onMouseLeave={(e) => {
                if (currentPage !== item.key) {
                  e.currentTarget.style.background = 'transparent'
                }
              }}
            >
              <div style={{
                fontSize: '20px',
                flexShrink: 0
              }}>
                {item.icon}
              </div>
              {!sidebarCollapsed && (
                <div>
                  <div style={{ fontSize: '20px', fontWeight: '800', marginBottom: '6px' }}>
                    {item.label}
                  </div>
                  <div style={{ 
                    fontSize: '16px', 
                    opacity: currentPage === item.key ? 0.9 : 0.7,
                    fontWeight: '600'
                  }}>
                    {item.description}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 主内容区 */}
      <div style={{
        flex: 1,
        marginLeft: sidebarCollapsed ? '80px' : '240px',
        transition: 'all 0.3s ease'
      }}>
        {/* 顶部导航 */}
        <div 
          className="theme-card theme-border"
          style={{
            backdropFilter: 'blur(20px)',
            padding: '20px 30px',
            borderBottom: '1px solid',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            position: 'sticky',
            top: 0,
            zIndex: 100
          }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
            <button
              onClick={toggleSidebar}
              style={{
                background: 'none',
                border: 'none',
                fontSize: '20px',
                cursor: 'pointer',
                padding: '8px',
                borderRadius: '8px',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(102, 126, 234, 0.1)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'none'
              }}
              onMouseDown={addButtonClickEffect}
            >
              ☰
            </button>
            <div>
              <h2 className="theme-text-primary" style={{ margin: 0, fontSize: '24px', fontWeight: '700' }}>
                {pageTitle}
              </h2>
              <p className="theme-text-secondary" style={{ margin: 0, fontSize: '14px' }}>
                {currentTime.toLocaleDateString('zh-CN', { 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric',
                  weekday: 'long' 
                })} {currentTime.toLocaleTimeString()}
              </p>
            </div>
          </div>
          
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            <ApiConnectionStatus 
              isConnected={isConnected} 
              connectionError={connectionError} 
            />
            <ThemeToggle />
            <div style={{
              background: 'rgba(102, 126, 234, 0.1)',
              padding: '8px 16px',
              borderRadius: '20px',
              fontSize: '14px',
              fontWeight: '600',
              color: '#667eea'
            }}>
              👤 系统管理员
            </div>
            <button
              onClick={() => setIsLoggedIn(false)}
              style={{
                background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)',
                color: 'white',
                border: 'none',
                padding: '10px 20px',
                borderRadius: '20px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-1px) scale(1)'
                e.currentTarget.style.boxShadow = '0 4px 15px rgba(255, 107, 107, 0.4)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0) scale(1)'
                e.currentTarget.style.boxShadow = 'none'
              }}
              onMouseDown={addButtonClickEffect}
            >
              🚪 安全退出
            </button>
          </div>
        </div>

        {/* 页面内容 */}
        <div style={{ padding: '30px' }}>
          {renderPageContent()}
        </div>
      </div>
    </div>
  )
}

export default App