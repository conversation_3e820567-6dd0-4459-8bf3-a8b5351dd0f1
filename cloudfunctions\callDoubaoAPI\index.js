/**
 * 通用AI模型调用云函数
 * 支持豆包AI的Chat Completions格式
 * 用于生成AI评语
 */
const cloud = require('wx-server-sdk');

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
  timeout: 60000 // 强制设置60秒超时
});

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  // 生产环境：隐藏敏感参数信息
  
  try {
    const { prompt, style = 'warm', length = 'medium', temperature = 0.7, max_tokens = 300, studentName = '', performanceMaterial = '' } = event;

    if (!prompt) {
      return {
        success: false,
        error: '提示词不能为空'
      };
    }

    // 处理提示词模板变量替换
    let processedPrompt = prompt;
    if (prompt.includes('{studentName}') || prompt.includes('{performanceMaterial}')) {
      processedPrompt = prompt
        .replace(/{studentName}/g, studentName || '该同学')
        .replace(/{performanceMaterial}/g, performanceMaterial || '暂无具体表现记录');
      
      // 生产环境：隐藏提示词内容
    }

    // 从云开发环境变量获取AI配置
    const aiConfig = {
      model: process.env.DOUBAO_MODEL || 'doubao-seed-1-6-flash-250715',
      apiUrl: process.env.DOUBAO_API_URL || 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
      apiKey: process.env.DOUBAO_API_KEY,
      provider: '豆包AI'
    };

    // 验证API密钥是否存在
    if (!aiConfig.apiKey) {
      console.error('DOUBAO_API_KEY 环境变量未配置');
      return {
        success: false,
        error: '系统配置错误：AI服务未正确配置',
        message: '请联系系统管理员配置AI服务'
      };
    }

    console.log('使用AI配置:', {
      model: aiConfig.model,
      hasApiKey: !!aiConfig.apiKey,
      apiUrl: aiConfig.apiUrl
    });

    // 尝试调用豆包AI API（设置较长超时时间）
    // API调用开始
    
    try {
      const response = await Promise.race([
        callAIAPI({
          prompt: processedPrompt,
          style,
          length,
          temperature,
          max_tokens,
          config: aiConfig
        }),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('豆包AI响应超时')), 50000) // 50秒超时
        )
      ]);
      
      // AI调用成功
      
      return {
        success: true,
        data: {
          content: response.content
        },
        message: '豆包AI生成成功',
        isFallback: false
      };
      
    } catch (aiError) {
      console.error('豆包AI API调用失败:', aiError);
      
      return {
        success: false,
        error: `豆包AI调用失败: ${aiError.message}`,
        message: '豆包AI服务异常，请稍后重试'
      };
    }

  } catch (error) {
    console.error('云函数执行失败:', error);
    
    return {
      success: false,
      error: `系统异常: ${error.message || '未知错误'}`,
      message: '云函数执行失败，请稍后重试'
    };
  }
};

/**
 * 调用AI API (使用内置https，避免axios加载时间)
 */
async function callAIAPI({ prompt, style, length, temperature, max_tokens, config }) {
  const https = require('https');
  const { URL } = require('url');

  // 构建请求数据 - 豆包AI专用格式 (支持content数组)
  const requestData = {
    model: config.model,
    messages: [
      {
        role: 'system',
        content: [
          {
            type: 'text',
            text: getSystemPrompt(style, length)
          }
        ]
      },
      {
        role: 'user', 
        content: [
          {
            type: 'text',
            text: prompt
          }
        ]
      }
    ],
    temperature: temperature,
    max_tokens: max_tokens,
    stream: false
  };

  // AI API调用中

  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(requestData);
    const parsedUrl = new URL(config.apiUrl);
    
    const options = {
      hostname: parsedUrl.hostname,
      port: 443,
      path: parsedUrl.pathname,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          
          if (res.statusCode === 200 && response.choices && response.choices[0]) {
            const content = response.choices[0].message.content;
            // AI内容生成完成
            
            resolve({
              content: content.trim(),
              usage: response.usage || {
                prompt_tokens: 0,
                completion_tokens: 0,
                total_tokens: 0
              }
            });
          } else {
            console.error('AI API响应错误:', response);
            reject(new Error(`API错误: ${response.error?.message || '未知错误'}`));
          }
        } catch (parseError) {
          console.error('解析响应失败:', parseError, 'Raw data:', data);
          reject(new Error('响应解析失败'));
        }
      });
    });

    req.on('error', (error) => {
      console.error('HTTPS请求失败:', error);
      reject(error);
    });

    // 不设置超时，等待AI完全生成内容
    // req.setTimeout() 已移除，让AI有充分时间生成内容

    req.write(postData);
    req.end();
  });
}

/**
 * 获取系统提示词
 */
function getSystemPrompt(style, length) {
  const stylePrompts = {
    'formal': '你是一位专业的教师，需要用正式、严谨的语言为学生写评语。评语应该客观、准确，体现专业性。',
    'warm': '你是一位温和亲切的老师，需要用温暖、关爱的语言为学生写评语。评语应该充满关怀，让学生感受到老师的温暖。',
    'encouraging': '你是一位善于激励学生的老师，需要用积极向上、鼓舞人心的语言为学生写评语。评语应该充满正能量，激发学生的潜力。',
    'detailed': '你是一位细致入微的老师，需要用详细具体的语言为学生写评语。评语应该全面、深入，提供具体的分析和建议。'
  };

  const lengthPrompts = {
    'short': '评语应该简洁明了，控制在50字以内。',
    'medium': '评语应该适中详细，控制在100字以内。',
    'long': '评语应该详细全面，控制在200字以内。'
  };

  let systemPrompt = stylePrompts[style] || stylePrompts['warm'];
  systemPrompt += ' ' + (lengthPrompts[length] || lengthPrompts['medium']);
  systemPrompt += ' 请确保评语内容积极正面，符合教育规范，有助于学生成长。';

  return systemPrompt;
}

/**
 * 基于学生记录生成智能评语
 */
function generateSmartComment(studentName, performanceMaterial, style = 'warm') {
  const name = studentName || '该同学';
  
  // 分析学生记录内容
  const records = performanceMaterial.toLowerCase();
  let evaluation = '';
  
  // 根据记录内容分析学生表现
  const positiveKeywords = ['帮助', '主动', '认真', '积极', '优秀', '表现好', '进步', '努力', '配合', '服务'];
  const academicKeywords = ['作业', '学习', '课堂', '考试', '成绩', '知识', '理解'];
  const behaviorKeywords = ['纪律', '迟到', '请假', '出勤', '违纪', '规范'];
  const socialKeywords = ['同学', '合作', '团结', '关系', '交流', '沟通'];
  
  let hasPositive = positiveKeywords.some(keyword => records.includes(keyword));
  let hasAcademic = academicKeywords.some(keyword => records.includes(keyword));
  let hasBehavior = behaviorKeywords.some(keyword => records.includes(keyword));
  let hasSocial = socialKeywords.some(keyword => records.includes(keyword));
  
  // 生成基于记录的个性化评语
  if (hasPositive) {
    switch (style) {
      case 'formal':
        evaluation = `${name}同学在本学期表现积极主动。从记录中可以看出，${name}具有良好的服务意识和责任感，能够主动承担班级事务，体现了较强的集体荣誉感。建议继续发扬这种积极向上的品质，在学习和生活中取得更大进步。`;
        break;
      case 'encouraging':
        evaluation = `${name}同学，你真的很棒！从你主动帮助老师和同学的行为中，老师看到了你的责任心和热心肠。这些优秀的品质将是你成功路上的宝贵财富，继续保持这份初心，你一定能够取得更大的成就！`;
        break;
      case 'detailed':
        evaluation = `${name}在本学期的表现值得肯定。在日常行为方面，能够主动承担责任，乐于助人，体现了良好的道德品质；在班级建设方面，积极参与各项活动，具有较强的集体意识。这些表现说明${name}正在成长为一名品学兼优的学生。建议在保持现有优点的基础上，继续在学业方面精进提高。`;
        break;
      default: // warm
        evaluation = `${name}是个很有爱心的孩子！老师从记录中看到你经常主动帮助大家，这种热心助人的品格让老师很感动。你的这份善良和责任心是最珍贵的品质，希望你继续保持这颗温暖的心，在今后的学习生活中发光发热。`;
    }
  } else {
    // 如果没有明显正面记录，给予鼓励性评语
    switch (style) {
      case 'formal':
        evaluation = `根据${name}的记录情况，建议在今后的学习生活中更加积极主动地参与班级活动，提升自己的综合素质。期待${name}在新的学期里能够有更好的表现。`;
        break;
      case 'encouraging':  
        evaluation = `${name}同学，每个人都有自己的闪光点！老师相信你一定有很多优秀的品质等待发掘。加油努力，勇敢地展示自己，你一定能够取得让大家刮目相看的进步！`;
        break;
      case 'detailed':
        evaluation = `${name}在本学期的整体表现中规中矩。建议在今后的学习生活中，能够更加主动地参与课堂讨论和班级活动，积极展现自己的能力和特长，相信通过努力，一定能够取得更大的进步。`;
        break;
      default: // warm
        evaluation = `${name}是个内秀的孩子，老师相信你内心一定很丰富。希望你能够更加自信一些，主动参与到班级的各项活动中来，让大家看到你的优秀品质。老师期待看到你更多精彩的表现！`;
    }
  }
  
  return evaluation;
}

