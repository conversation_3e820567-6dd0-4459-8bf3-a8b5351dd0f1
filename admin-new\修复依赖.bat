@echo off
chcp 65001 >nul
title 评语灵感君 - 依赖修复工具

:main
cls
echo.
echo ╔════════════════════════════════════════╗
echo ║        评语灵感君依赖修复工具          ║
echo ╚════════════════════════════════════════╝
echo.
echo 🚨 将要执行以下操作：
echo    1. 删除 node_modules 文件夹
echo    2. 删除 package-lock.json 文件  
echo    3. 清理 npm 缓存
echo    4. 重新安装依赖
echo    5. 启动开发服务器
echo.
echo ⚠️  这个过程可能需要5-10分钟
echo.
set /p confirm="确认继续吗？(y/n): "
if /i not "%confirm%"=="y" goto :cancel

echo.
echo ════════════════════════════════════════
echo 🚀 开始修复过程...
echo ════════════════════════════════════════

rem 步骤1: 清理旧文件
echo.
echo 📁 [1/5] 清理旧文件...
if exist "node_modules" (
    echo    正在删除 node_modules 文件夹...
    rmdir /s /q "node_modules" 2>nul
    if exist "node_modules" (
        echo    ❌ 无法删除 node_modules，可能有文件被占用
        echo    请关闭所有相关程序后重试
        goto :error
    )
    echo    ✅ node_modules 已删除
) else (
    echo    📝 node_modules 不存在，跳过
)

if exist "package-lock.json" (
    echo    正在删除 package-lock.json...
    del "package-lock.json" 2>nul
    echo    ✅ package-lock.json 已删除
)

if exist "yarn.lock" (
    echo    正在删除 yarn.lock...
    del "yarn.lock" 2>nul
    echo    ✅ yarn.lock 已删除
)

rem 步骤2: 检查环境
echo.
echo 🔍 [2/5] 检查开发环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo    ❌ 未找到 Node.js，请先安装 Node.js
    echo    下载地址: https://nodejs.org/
    goto :error
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo    ✅ Node.js 版本: %NODE_VERSION%

npm --version >nul 2>&1
if errorlevel 1 (
    echo    ❌ 未找到 npm
    goto :error
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo    ✅ npm 版本: %NPM_VERSION%

rem 步骤3: 清理缓存
echo.
echo 🧹 [3/5] 清理 npm 缓存...
call npm cache clean --force
if errorlevel 1 (
    echo    ⚠️ 缓存清理失败，但继续执行...
) else (
    echo    ✅ npm 缓存已清理
)

rem 步骤4: 安装依赖
echo.
echo 📦 [4/5] 安装项目依赖...
echo    这可能需要几分钟时间，请耐心等待...
echo.

rem 尝试方法1: npm install --legacy-peer-deps
echo    🔄 尝试方法1: npm install --legacy-peer-deps
call npm install --legacy-peer-deps --no-audit --progress=false
if not errorlevel 1 (
    echo    ✅ 方法1成功！依赖安装完成
    goto :verify
)

echo    ❌ 方法1失败，尝试方法2...

rem 尝试方法2: 标准 npm install
echo    🔄 尝试方法2: npm install
call npm install --no-audit --progress=false
if not errorlevel 1 (
    echo    ✅ 方法2成功！依赖安装完成
    goto :verify
)

echo    ❌ 方法2失败，尝试方法3...

rem 尝试方法3: yarn
echo    🔄 尝试方法3: 使用 yarn
yarn --version >nul 2>&1
if errorlevel 1 (
    echo    安装 yarn...
    call npm install -g yarn --force
    if errorlevel 1 (
        echo    ❌ yarn 安装失败
        goto :install_failed
    )
)

call yarn install
if not errorlevel 1 (
    echo    ✅ 方法3成功！依赖安装完成
    goto :verify
)

:install_failed
echo    ❌ 所有安装方法都失败了
echo.
echo    🛠️ 可能的解决方案:
echo    1. 检查网络连接
echo    2. 使用 VPN 或更换网络
echo    3. 以管理员身份运行此脚本
echo    4. 升级 Node.js 到最新版本
echo.
goto :error

:verify
rem 步骤5: 验证安装
echo.
echo ✅ [5/5] 验证安装结果...

if not exist "node_modules" (
    echo    ❌ node_modules 文件夹不存在
    goto :error
)

if not exist "node_modules\.bin\vite.cmd" (
    echo    ❌ vite 未正确安装
    goto :error
)

echo    ✅ 关键依赖验证通过

rem 询问是否启动
echo.
echo ════════════════════════════════════════
echo 🎉 依赖修复完成！
echo ════════════════════════════════════════
echo.
set /p start="是否立即启动开发服务器？(y/n): "
if /i "%start%"=="y" goto :start_dev

echo.
echo 💡 手动启动命令: npm run dev
echo.
goto :success

:start_dev
echo.
echo 🚀 启动开发服务器...
echo    浏览器将自动打开 http://localhost:3000
echo    按 Ctrl+C 停止服务器
echo.
call npm run dev
goto :end

:cancel
echo.
echo 📝 操作已取消
goto :end

:error
echo.
echo ════════════════════════════════════════
echo ❌ 修复过程中出现错误
echo ════════════════════════════════════════
echo.
echo 🛠️ 手动修复步骤:
echo    1. 删除 node_modules 文件夹
echo    2. 删除 package-lock.json 文件
echo    3. 运行: npm cache clean --force
echo    4. 运行: npm install --legacy-peer-deps
echo.
goto :end

:success
echo ✅ 所有操作完成！
echo.

:end
echo.
echo ════════════════════════════════════════
echo 按任意键退出...
echo ════════════════════════════════════════
pause >nul
exit /b