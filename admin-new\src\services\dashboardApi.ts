import apiClient from './apiClient'

// 仪表板统计数据接口
export interface DashboardStats {
  totalUsers: number
  todayComments: number
  aiCalls: number
  satisfaction: number
  lastUpdated: string
}

// 系统活动记录接口
export interface ActivityRecord {
  id: string
  userId: string
  userName: string
  userAvatar?: string
  action: string
  actionType: 'comment_generate' | 'data_import' | 'config_update' | 'report_export' | 'batch_operation'
  timestamp: string
  metadata?: Record<string, any>
}

// 系统性能指标接口
export interface SystemMetrics {
  cpu: number
  memory: number
  storage: number
  apiResponseTime: number
  activeConnections: number
  timestamp: string
}

/**
 * 获取仪表板统计数据
 */
export const getDashboardStats = async (): Promise<DashboardStats> => {
  try {
    const response = await apiClient.post('', {
      action: 'data.getDashboardStats'
    })
    
    if (response.data.code === 200) {
      return response.data.data
    } else {
      throw new Error(response.data.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取仪表板统计数据失败:', error)
    
    // 返回默认空数据
    return {
      totalUsers: 0,
      todayComments: 0,
      aiCalls: 0,
      satisfaction: 0,
      lastUpdated: new Date().toISOString()
    }
  }
}

/**
 * 获取最近活动记录
 */
export const getRecentActivities = async (limit = 10): Promise<ActivityRecord[]> => {
  try {
    const response = await apiClient.post('', {
      action: 'data.getRecentActivities',
      limit
    })
    
    if (response.data.code === 200) {
      return response.data.data
    } else {
      throw new Error(response.data.message || '获取活动记录失败')
    }
  } catch (error) {
    console.error('获取活动记录失败:', error)
    return []
  }
}

/**
 * 获取系统性能指标
 */
export const getSystemMetrics = async (): Promise<SystemMetrics> => {
  try {
    const response = await apiClient.post('', {
      action: 'data.getSystemMetrics'
    })
    
    if (response.data.code === 200) {
      return response.data.data
    } else {
      throw new Error(response.data.message || '获取系统指标失败')
    }
  } catch (error) {
    console.error('获取系统指标失败:', error)
    
    // 返回默认数据
    return {
      cpu: 0,
      memory: 0,
      storage: 0,
      apiResponseTime: 0,
      activeConnections: 0,
      timestamp: new Date().toISOString()
    }
  }
}

/**
 * 获取用户统计数据
 */
export const getUserStats = async () => {
  try {
    const response = await apiClient.get('/admin/users/stats')
    return response.data
  } catch (error) {
    console.error('获取用户统计失败:', error)
    return {
      totalUsers: 0,
      activeUsers: 0,
      newUsersToday: 0,
      userGrowthRate: 0
    }
  }
}

/**
 * 获取评语生成统计
 */
export const getCommentStats = async () => {
  try {
    const response = await apiClient.get('/admin/comments/stats')
    return response.data
  } catch (error) {
    console.error('获取评语统计失败:', error)
    return {
      totalComments: 0,
      todayComments: 0,
      averageLength: 0,
      popularTemplates: []
    }
  }
}

/**
 * 获取AI调用统计
 */
export const getAIStats = async () => {
  try {
    const response = await apiClient.get('/admin/ai/stats')
    return response.data
  } catch (error) {
    console.error('获取AI统计失败:', error)
    return {
      totalCalls: 0,
      todayCalls: 0,
      averageResponseTime: 0,
      successRate: 0,
      tokenUsage: 0
    }
  }
}

/**
 * 获取系统健康状态
 */
export const getSystemHealth = async () => {
  try {
    const response = await apiClient.get('/admin/system/health')
    return response.data
  } catch (error) {
    console.error('获取系统健康状态失败:', error)
    return {
      status: 'unknown',
      uptime: 0,
      version: '1.0.0',
      environment: 'development',
      lastCheck: new Date().toISOString()
    }
  }
}

/**
 * 获取实时在线用户数
 */
export const getOnlineUsers = async () => {
  try {
    const response = await apiClient.get('/admin/users/online')
    return response.data
  } catch (error) {
    console.error('获取在线用户失败:', error)
    return {
      count: 0,
      users: []
    }
  }
}

/**
 * 导出仪表板数据
 */
export const exportDashboardData = async (format: 'excel' | 'csv' = 'excel') => {
  try {
    const response = await apiClient.get(`/admin/dashboard/export?format=${format}`, {
      responseType: 'blob'
    })
    
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `dashboard-data-${new Date().toISOString().split('T')[0]}.${format}`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)
    
    return true
  } catch (error) {
    console.error('导出数据失败:', error)
    return false
  }
}

// 实用工具函数
export const formatActivityAction = (activity: ActivityRecord): string => {
  const actionMap = {
    comment_generate: '生成了评语',
    data_import: '导入了数据',
    config_update: '更新了配置',
    report_export: '导出了报告',
    batch_operation: '执行了批量操作'
  }
  
  return actionMap[activity.actionType] || activity.action
}

export const getActivityIcon = (actionType: ActivityRecord['actionType']) => {
  const iconMap = {
    comment_generate: 'MessageOutlined',
    data_import: 'ImportOutlined',
    config_update: 'SettingOutlined',
    report_export: 'ExportOutlined',
    batch_operation: 'ThunderboltOutlined'
  }
  
  return iconMap[actionType] || 'InfoCircleOutlined'
}

export const getActivityColor = (actionType: ActivityRecord['actionType']) => {
  const colorMap = {
    comment_generate: 'bg-blue-500',
    data_import: 'bg-green-500',
    config_update: 'bg-purple-500',
    report_export: 'bg-orange-500',
    batch_operation: 'bg-pink-500'
  }
  
  return colorMap[actionType] || 'bg-gray-500'
}

export default {
  getDashboardStats,
  getRecentActivities,
  getSystemMetrics,
  getUserStats,
  getCommentStats,
  getAIStats,
  getSystemHealth,
  getOnlineUsers,
  exportDashboardData,
  formatActivityAction,
  getActivityIcon,
  getActivityColor
}