# 🛠 故障排除指南

## 🚨 常见问题与解决方案

### 1. Rollup依赖错误
**错误信息**: `Cannot find module @rollup/rollup-win32-x64-msvc`

**原因**: npm的已知bug，影响Windows系统上的rollup依赖

**解决方案**:
```bash
# 方法1: 使用修复脚本（推荐）
.\fix-deps.bat

# 方法2: 手动清理
rmdir /s /q node_modules
del package-lock.json
npm install

# 方法3: 使用yarn
npm install -g yarn
yarn install
```

### 2. 依赖安装失败
**错误信息**: `npm ERR! code ENOENT` 或网络错误

**解决方案**:
```bash
# 清理npm缓存
npm cache clean --force

# 使用国内镜像
npm config set registry https://registry.npmmirror.com/

# 重新安装
npm install
```

### 3. 端口被占用
**错误信息**: `Port 3000 is already in use`

**解决方案**:
1. 修改 `.env.local` 文件:
```bash
VITE_DEV_SERVER_PORT=3001
```

2. 或者杀死占用端口的进程:
```bash
# 查找占用端口的进程
netstat -ano | findstr :3000

# 杀死进程（替换PID）
taskkill /PID [进程ID] /F
```

### 4. TypeScript编译错误
**错误信息**: `TS2307: Cannot find module` 或类型错误

**解决方案**:
```bash
# 重新生成类型声明
npm run type-check

# 清理TypeScript缓存
npx tsc --build --clean
```

### 5. Vite启动失败
**错误信息**: `Failed to resolve import` 或模块解析错误

**解决方案**:
```bash
# 清理Vite缓存
rm -rf node_modules/.vite
# 或Windows下
rmdir /s /q node_modules\.vite

# 重启开发服务器
npm run dev
```

### 6. 环境变量不生效
**问题**: 修改.env文件后配置不生效

**解决方案**:
1. 确保环境变量以 `VITE_` 开头
2. 重启开发服务器: `Ctrl+C` 然后 `npm run dev`
3. 检查文件编码为UTF-8
4. 确保没有多余的空格

### 7. API连接失败
**错误信息**: `Network Error` 或 `CORS` 错误

**解决方案**:
1. 检查云函数是否已部署:
   - 打开微信开发者工具
   - 查看云开发 → 云函数
   - 确认 `adminAPI` 函数存在且已部署

2. 启用Mock模式进行开发:
   ```bash
   # 编辑 .env.local
   VITE_ENABLE_MOCK=true
   ```

3. 检查环境ID是否正确:
   ```bash
   # 在 .env.local 中确认
   VITE_WECHAT_CLOUD_ENV_ID=cloud1-4g85f8xlb8166ff1
   ```

## 🔧 诊断工具

### 1. 环境检查
```bash
# 检查Node.js版本
node --version

# 检查npm版本
npm --version

# 验证环境配置
.\validate.bat
```

### 2. 依赖检查
```bash
# 检查依赖完整性
npm ls

# 检查过时的依赖
npm outdated

# 安全审计
npm audit
```

### 3. 网络检查
```bash
# 测试npm连接
npm ping

# 检查代理设置
npm config get proxy
npm config get https-proxy
```

## 🚀 性能优化

### 1. 加快依赖安装
```bash
# 使用cnpm
npm install -g cnpm --registry=https://registry.npmmirror.com/
cnpm install

# 或使用pnpm
npm install -g pnpm
pnpm install
```

### 2. 加快开发启动
```bash
# 启用快速刷新
# 已在vite.config.ts中配置
```

## 📞 获取帮助

### 1. 日志收集
如果问题持续存在，请收集以下信息:
- Node.js版本: `node --version`
- npm版本: `npm --version`
- 操作系统版本
- 完整的错误堆栈信息
- package.json内容

### 2. 重置项目
如果所有方法都失败，可以重置项目:
```bash
# 完全清理
rmdir /s /q node_modules
del package-lock.json
del -rf .vite

# 重新开始
npm install
npm run dev
```

### 3. 降级方案
如果实在无法解决，可以尝试:
1. 使用yarn替代npm
2. 降级Node.js版本到LTS
3. 在不同的目录重新创建项目

## ✅ 验证修复

修复问题后，请验证:
1. 运行 `npm run dev` 无错误
2. 浏览器正常打开应用
3. 页面功能正常工作
4. 控制台无严重错误

---

**记住**: 大多数问题都可以通过清理依赖和重新安装来解决！