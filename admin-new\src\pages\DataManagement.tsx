import React, { useState } from 'react'
import { 
  Table, 
  Button, 
  Space, 
  Typography, 
  Input, 
  Select, 
  DatePicker, 
  Card,
  Tabs,
  Upload,
  message,
  Modal,
  Tag,
  Drawer,
  Descriptions
} from 'antd'
import { 
  SearchOutlined, 
  DownloadOutlined, 
  UploadOutlined, 
  DeleteOutlined,
  EyeOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { RangePickerProps } from 'antd/es/date-picker'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { Search } = Input
const { Option } = Select
const { RangePicker } = DatePicker
const { TabPane } = Tabs
const { confirm } = Modal

interface Student {
  id: string
  name: string
  class: string
  teacher: string
  commentsCount: number
  lastUpdate: string
  status: 'active' | 'inactive'
}

interface Comment {
  id: string
  studentName: string
  className: string
  teacher: string
  content: string
  type: string
  createTime: string
  aiModel: string
  status: 'success' | 'failed'
}

const DataManagement: React.FC = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<any>(null)

  // 模拟学生数据
  const studentsData: Student[] = [
    {
      id: '1',
      name: '张小明',
      class: '三年级一班',
      teacher: '王老师',
      commentsCount: 12,
      lastUpdate: '2024-01-27 10:30:00',
      status: 'active'
    },
    {
      id: '2',
      name: '李小红',
      class: '三年级一班', 
      teacher: '王老师',
      commentsCount: 8,
      lastUpdate: '2024-01-26 15:20:00',
      status: 'active'
    },
    {
      id: '3',
      name: '陈小华',
      class: '三年级二班',
      teacher: '张老师',
      commentsCount: 15,
      lastUpdate: '2024-01-25 09:45:00',
      status: 'inactive'
    }
  ]

  // 模拟评语数据
  const commentsData: Comment[] = [
    {
      id: '1',
      studentName: '张小明',
      className: '三年级一班',
      teacher: '王老师',
      content: '张小明同学在本周表现优秀，数学作业完成质量很高...',
      type: '温暖鼓励型',
      createTime: '2024-01-27 10:30:00',
      aiModel: '豆包模型',
      status: 'success'
    },
    {
      id: '2',
      studentName: '李小红',
      className: '三年级一班',
      teacher: '王老师', 
      content: '李小红同学这周在语文课上积极发言，朗读能力有明显提升...',
      type: '客观分析型',
      createTime: '2024-01-26 15:20:00',
      aiModel: '豆包模型',
      status: 'success'
    }
  ]

  const studentColumns: ColumnsType<Student> = [
    {
      title: '学生姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120
    },
    {
      title: '班级',
      dataIndex: 'class',
      key: 'class',
      width: 120
    },
    {
      title: '任课教师',
      dataIndex: 'teacher',
      key: 'teacher',
      width: 100
    },
    {
      title: '评语数量',
      dataIndex: 'commentsCount',
      key: 'commentsCount',
      width: 100,
      sorter: (a, b) => a.commentsCount - b.commentsCount
    },
    {
      title: '最后更新',
      dataIndex: 'lastUpdate',
      key: 'lastUpdate',
      width: 160,
      sorter: (a, b) => dayjs(a.lastUpdate).unix() - dayjs(b.lastUpdate).unix()
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={status === 'active' ? 'success' : 'default'}>
          {status === 'active' ? '活跃' : '非活跃'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="text" 
            icon={<EyeOutlined />} 
            size="small"
            onClick={() => handleViewDetail(record)}
          >
            查看
          </Button>
          <Button 
            type="text" 
            icon={<DeleteOutlined />} 
            size="small" 
            danger
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      )
    }
  ]

  const commentColumns: ColumnsType<Comment> = [
    {
      title: '学生姓名',
      dataIndex: 'studentName',
      key: 'studentName',
      width: 100
    },
    {
      title: '班级',
      dataIndex: 'className',
      key: 'className',
      width: 120
    },
    {
      title: '教师',
      dataIndex: 'teacher',
      key: 'teacher',
      width: 80
    },
    {
      title: '评语内容',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
      render: (text: string) => text.length > 50 ? text.substring(0, 50) + '...' : text
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: string) => <Tag color="blue">{type}</Tag>
    },
    {
      title: 'AI模型',
      dataIndex: 'aiModel',
      key: 'aiModel',
      width: 100
    },
    {
      title: '生成时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
      sorter: (a, b) => dayjs(a.createTime).unix() - dayjs(b.createTime).unix()
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={status === 'success' ? 'success' : 'error'}>
          {status === 'success' ? '成功' : '失败'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="text" 
            icon={<EyeOutlined />} 
            size="small"
            onClick={() => handleViewDetail(record)}
          >
            查看
          </Button>
          <Button 
            type="text" 
            icon={<DeleteOutlined />} 
            size="small" 
            danger
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      )
    }
  ]

  const handleViewDetail = (record: any) => {
    setSelectedRecord(record)
    setDetailDrawerVisible(true)
  }

  const handleDelete = (id: string) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: '删除后数据将无法恢复，确定要删除吗？',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        message.success('删除成功')
      }
    })
  }

  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的数据')
      return
    }
    
    confirm({
      title: '批量删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除选中的 ${selectedRowKeys.length} 条数据吗？`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        setSelectedRowKeys([])
        message.success('批量删除成功')
      }
    })
  }

  const handleExport = () => {
    message.success('数据导出中，请稍候...')
  }

  const uploadProps = {
    name: 'file',
    action: '/api/upload',
    onChange(info: any) {
      if (info.file.status === 'done') {
        message.success('数据导入成功')
      } else if (info.file.status === 'error') {
        message.error('数据导入失败')
      }
    }
  }

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(selectedRowKeys)
    }
  }

  return (
    <div>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <div>
          <Title level={2} style={{ marginBottom: 8 }}>
            数据管理
          </Title>
          <Text type="secondary">
            管理学生信息和评语数据
          </Text>
        </div>

        <Tabs defaultActiveKey="students" type="card">
          <TabPane tab="学生管理" key="students">
            <Card>
              {/* 搜索和操作栏 */}
              <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap', gap: 16 }}>
                <Space wrap>
                  <Search
                    placeholder="搜索学生姓名"
                    style={{ width: 200 }}
                    onSearch={(value) => console.log(value)}
                  />
                  <Select placeholder="选择班级" style={{ width: 120 }}>
                    <Option value="1">三年级一班</Option>
                    <Option value="2">三年级二班</Option>
                  </Select>
                  <Select placeholder="选择状态" style={{ width: 100 }}>
                    <Option value="active">活跃</Option>
                    <Option value="inactive">非活跃</Option>
                  </Select>
                </Space>
                
                <Space wrap>
                  <Upload {...uploadProps}>
                    <Button icon={<UploadOutlined />}>
                      导入学生
                    </Button>
                  </Upload>
                  <Button 
                    icon={<DownloadOutlined />}
                    onClick={handleExport}
                  >
                    导出数据
                  </Button>
                  <Button 
                    danger
                    icon={<DeleteOutlined />}
                    onClick={handleBatchDelete}
                    disabled={selectedRowKeys.length === 0}
                  >
                    批量删除
                  </Button>
                </Space>
              </div>

              <Table
                columns={studentColumns}
                dataSource={studentsData}
                rowKey="id"
                rowSelection={rowSelection}
                pagination={{
                  total: studentsData.length,
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                }}
                scroll={{ x: 800 }}
              />
            </Card>
          </TabPane>

          <TabPane tab="评语管理" key="comments">
            <Card>
              {/* 搜索和操作栏 */}
              <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap', gap: 16 }}>
                <Space wrap>
                  <Search
                    placeholder="搜索学生或内容"
                    style={{ width: 200 }}
                    onSearch={(value) => console.log(value)}
                  />
                  <Select placeholder="评语类型" style={{ width: 120 }}>
                    <Option value="encourage">鼓励型</Option>
                    <Option value="analysis">分析型</Option>
                  </Select>
                  <RangePicker placeholder={['开始时间', '结束时间']} />
                </Space>
                
                <Space wrap>
                  <Button 
                    icon={<DownloadOutlined />}
                    onClick={handleExport}
                  >
                    导出评语
                  </Button>
                  <Button 
                    danger
                    icon={<DeleteOutlined />}
                    onClick={handleBatchDelete}
                    disabled={selectedRowKeys.length === 0}
                  >
                    批量删除
                  </Button>
                </Space>
              </div>

              <Table
                columns={commentColumns}
                dataSource={commentsData}
                rowKey="id"
                rowSelection={rowSelection}
                pagination={{
                  total: commentsData.length,
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                }}
                scroll={{ x: 1000 }}
              />
            </Card>
          </TabPane>
        </Tabs>
      </Space>

      {/* 详情抽屉 */}
      <Drawer
        title="详细信息"
        placement="right"
        size="large"
        onClose={() => setDetailDrawerVisible(false)}
        open={detailDrawerVisible}
      >
        {selectedRecord && (
          <Descriptions column={1} bordered>
            {selectedRecord.name && (
              <Descriptions.Item label="学生姓名">
                {selectedRecord.name}
              </Descriptions.Item>
            )}
            {selectedRecord.studentName && (
              <Descriptions.Item label="学生姓名">
                {selectedRecord.studentName}
              </Descriptions.Item>
            )}
            <Descriptions.Item label="班级">
              {selectedRecord.class || selectedRecord.className}
            </Descriptions.Item>
            <Descriptions.Item label="教师">
              {selectedRecord.teacher}
            </Descriptions.Item>
            {selectedRecord.content && (
              <Descriptions.Item label="评语内容">
                {selectedRecord.content}
              </Descriptions.Item>
            )}
            {selectedRecord.type && (
              <Descriptions.Item label="评语类型">
                <Tag color="blue">{selectedRecord.type}</Tag>
              </Descriptions.Item>
            )}
            {selectedRecord.commentsCount && (
              <Descriptions.Item label="评语数量">
                {selectedRecord.commentsCount}
              </Descriptions.Item>
            )}
            <Descriptions.Item label="更新时间">
              {selectedRecord.lastUpdate || selectedRecord.createTime}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Drawer>
    </div>
  )
}

export default DataManagement