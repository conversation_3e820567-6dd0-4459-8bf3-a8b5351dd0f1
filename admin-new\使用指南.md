# 🚀 评语灵感君管理后台 v2.0 使用指南

## 📋 快速启动（推荐）

### 方式1：使用启动脚本（最简单）

**CMD用户**: 双击运行 `start.bat` 文件

**PowerShell用户**: 右键点击 `start.ps1` → "使用PowerShell运行"

脚本会自动：
- 安装依赖
- 验证配置
- 启动开发服务器

### 方式2：命令行启动
```bash
# 1. 安装依赖（首次运行）
npm install

# 2. 启动开发服务器
npm run dev
```

## 🔧 配置状态

### ✅ 已配置的文件
- `.env.local` - 本地开发配置（主要文件）
- `.env.development` - 开发环境配置
- `.env.production` - 生产环境配置

### 🌟 关键配置信息
```
云环境ID: cloud1-4g85f8xlb8166ff1
API地址: https://cloud1-4g85f8xlb8166ff1.ap-beijing.app.tcloudbase.com/adminAPI
开发端口: 3000
```

## 🛠 可选操作

### 验证配置
双击运行 `validate.bat` 或执行：
```bash
node scripts\validate-env.js
```

### 启用Mock模式
如果云函数暂时无法访问，编辑 `.env.local`：
```bash
VITE_ENABLE_MOCK=true
```

## 🎯 访问应用

启动成功后，浏览器会自动打开：
- **本地地址**: http://localhost:3000
- **默认账号**: admin / admin123 (Mock模式下)

## 📱 功能模块

### 已实现的页面
1. **登录页面** - 用户认证
2. **仪表板** - 数据概览和图表
3. **AI配置** - 多服务商配置管理
4. **模板编辑器** - 评语模板可视化编辑
5. **使用监控** - API调用统计和成本分析
6. **系统设置** - 用户和系统配置

### 技术特性
- ✅ TypeScript类型安全
- ✅ 响应式设计（支持移动端）
- ✅ 暗色模式支持
- ✅ 错误边界保护
- ✅ 加载状态优化
- ✅ 路由懒加载

## 🐛 常见问题

### Q: Rollup依赖错误（Windows常见）
**错误**: `Cannot find module @rollup/rollup-win32-x64-msvc`

**解决方案**:

**CMD用户**: 双击运行 `fix-deps.bat`

**PowerShell用户**: 运行 `.\fix-deps.ps1` 或手动执行：
```powershell
Remove-Item -Path "node_modules" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "package-lock.json" -Force -ErrorAction SilentlyContinue
npm install
```

**详细说明**: 查看 `PowerShell命令.md` 文件

### Q: 启动失败？
A: 确保Node.js版本 >= 18.0.0，运行 `node --version` 检查

### Q: 端口被占用？
A: 修改 `.env.local` 中的 `VITE_DEV_SERVER_PORT=3001`

### Q: API连接失败？
A: 
1. 确认云函数 `adminAPI` 已部署
2. 检查网络连接
3. 临时启用Mock模式继续开发

### Q: 各种依赖问题？
A: 查看 `故障排除.md` 文件获取详细解决方案

## 🚀 开发流程

### 1. 启动开发
- 运行 `start.bat` 或 `npm run dev`
- 浏览器自动打开到 http://localhost:3000

### 2. 开发调试
- 修改代码后页面自动刷新
- 控制台查看错误信息
- 使用React DevTools调试

### 3. 构建部署
```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview

# 部署到云开发
npm run deploy
```

## 📞 获取帮助

### 项目文件
- `README.md` - 详细技术文档
- `CONFIGURATION.md` - 配置详细说明
- `QUICK_START.md` - 快速开始指南

### 调试工具
- 浏览器开发者工具 (F12)
- React DevTools 扩展
- 环境验证脚本

## 🎉 开始使用

现在所有配置都已准备就绪！

**立即开始：双击 `start.bat` 文件启动项目！**

享受现代化的管理后台开发体验！ 🚀