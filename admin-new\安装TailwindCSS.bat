@echo off
echo =============================================
echo   安装TailwindCSS依赖
echo =============================================
echo.

echo 🎨 安装TailwindCSS和相关依赖...
echo.

echo 步骤1: 安装TailwindCSS核心包
npm install -D tailwindcss postcss autoprefixer

echo.
echo 步骤2: 检查安装结果
if exist "node_modules\tailwindcss" (
    echo ✅ TailwindCSS 安装成功
) else (
    echo ❌ TailwindCSS 安装失败，尝试强制安装...
    npm install -D tailwindcss postcss autoprefixer --force
)

if exist "node_modules\autoprefixer" (
    echo ✅ Autoprefixer 安装成功
) else (
    echo ❌ Autoprefixer 安装失败
)

echo.
echo 步骤3: 重启开发服务器
echo ✅ 依赖安装完成！重启服务器...
echo.
echo 请按 Ctrl+C 停止当前服务器，然后运行:
echo npm run dev
echo.

pause