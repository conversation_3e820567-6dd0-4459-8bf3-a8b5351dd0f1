import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App'
import { initializeTheme } from '@/stores/themeStore'
import '@/styles/theme.css'

console.log('🚀 开始加载React应用...')

// 初始化主题
initializeTheme()

const rootElement = document.getElementById('root')
if (!rootElement) {
  throw new Error('找不到root元素！')
}

const root = ReactDOM.createRoot(rootElement)
root.render(<App />)

console.log('✅ React应用渲染完成！')