/**
 * 获取提示词模板云函数
 * 专门为小程序端提供模板获取服务，无需管理员权限
 */

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV  
});

const db = cloud.database();
const _ = db.command;

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { type, version } = event;
  
  try {
    console.log(`[getPromptTemplate] 请求获取模板: type=${type}, version=${version}`);
    
    if (!type) {
      throw new Error('模板类型不能为空');
    }
    
    // 查询模板数据
    let query = db.collection('prompt_templates').where({
      type: type,
      enabled: true
    });
    
    // 如果指定了版本，则查询特定版本
    if (version) {
      query = query.where({
        version: version
      });
    }
    
    // 获取最新版本的模板
    const templatesResult = await query.orderBy('version', 'desc').limit(1).get();
    
    if (templatesResult.data.length === 0) {
      console.log(`[getPromptTemplate] 未找到类型为${type}的模板，使用默认模板`);
      
      // 如果没有找到模板，返回默认模板
      const defaultTemplate = getDefaultTemplate(type);
      return {
        success: true,
        data: defaultTemplate,
        source: 'default'
      };
    }
    
    const template = templatesResult.data[0];
    console.log(`[getPromptTemplate] 找到模板: ${template.name}, 版本: ${template.version}`);
    
    // 转换为统一格式
    const formattedTemplate = {
      id: template._id,
      name: template.name,
      type: template.type,
      content: template.content,
      variables: template.variables || [],
      metadata: template.metadata || {},
      version: template.version || 1,
      enabled: template.enabled !== false,
      source: 'database',
      cachedAt: Date.now(),
      cacheVersion: 2
    };
    
    // 更新使用统计
    try {
      await db.collection('prompt_templates').doc(template._id).update({
        data: {
          'metadata.usageCount': _.inc(1),
          'metadata.lastUsedAt': new Date().toISOString()
        }
      });
    } catch (updateError) {
      console.warn('[getPromptTemplate] 更新使用统计失败:', updateError);
      // 不影响主流程
    }
    
    return {
      success: true,
      data: formattedTemplate,
      source: 'database'
    };
    
  } catch (error) {
    console.error('[getPromptTemplate] 获取模板失败:', error);
    
    // 发生错误时返回默认模板作为兜底
    try {
      const defaultTemplate = getDefaultTemplate(type || 'warm');
      return {
        success: true,
        data: defaultTemplate,
        source: 'fallback',
        error: error.message
      };
    } catch (fallbackError) {
      return {
        success: false,
        error: error.message || '获取模板失败',
        fallbackError: fallbackError.message
      };
    }
  }
};

/**
 * 获取默认模板（兜底方案）
 */
function getDefaultTemplate(type) {
  const defaultTemplates = {
    'formal': {
      id: 'formal_default',
      name: '正式评语模板（默认）',
      type: 'formal',
      content: `你是一位拥有15年教龄、经验丰富、充满爱心且善于观察的中职班主任。你的任务是根据提供的学生日常表现素材，为学生撰写一份全面、客观、个性化、充满关怀的学期综合评语。

学生姓名：
<student_name>
{studentName}
</student_name>

学生本学期的日常表现素材如下：
<performance_material>
{performanceMaterial}
</performance_material>

请严格遵循以下要求撰写评语：
1. **评语结构**：采用"总体评价 + 优点详述 + 待改进点与期望 + 结尾祝福"的结构。
2. **内容要求**：
   - **优点详述部分**：必须从素材中提炼2-3个最突出的优点，并引用具体事例来支撑，让表扬不空洞。
   - **待改进点部分**：如果素材中有相关记录，请用委婉、鼓励的语气指出，并提出具体、可行的建议。如果没有，则可以写一些普遍性的鼓励和期望。
3. **语气风格**：语言要正式、客观、专业，体现中职老师的权威性和专业性，称呼以学生名字（不带姓，如小华同学）开头。
4. **个性化**：评语必须尊重、保留且紧密结合提供的素材，体现出每个学生的独特性，严禁使用千篇一律的套话，严禁捏造素材中不存在的内容。
5. **日常行为记录缺失处理要求**：当表现素材为空时，请基于该学生的基本信息（姓名、班级等）生成一份积极正面的通用评语，重点关注学习态度、成长潜力和未来期望，避免具体事例描述。

请直接生成完整的评语内容，100-150字之间。`,
      variables: [
        { name: 'studentName', type: 'string', required: true, description: '学生姓名' },
        { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料' }
      ],
      metadata: {
        author: 'system',
        description: '正式严谨的评语模板',
        tags: ['formal', 'professional', 'default'],
        usageCount: 0
      },
      version: 1,
      enabled: true,
      source: 'hardcoded',
      cachedAt: Date.now(),
      cacheVersion: 2
    },
    
    'warm': {
      id: 'warm_default',
      name: '温馨评语模板（默认）',
      type: 'warm',
      content: `你是一位拥有15年教龄、经验丰富、充满爱心且善于观察的中职班主任。你的任务是根据提供的学生日常表现素材，为学生撰写一份温馨亲切、充满关怀的学期综合评语。

学生姓名：
<student_name>
{studentName}
</student_name>

学生本学期的日常表现素材如下：
<performance_material>
{performanceMaterial}
</performance_material>

请严格遵循以下要求撰写评语：
1. **评语结构**：采用"亲切问候 + 优点赞扬 + 温馨建议 + 暖心祝福"的结构。
2. **内容要求**：
   - **优点赞扬部分**：必须从素材中提炼2-3个最突出的优点，用温暖的语言表达赞扬，让学生感受到被认可。
   - **温馨建议部分**：如果素材中有需要改进的地方，请用关爱、鼓励的语气提出建议。如果没有，则给予温暖的期望和鼓励。
3. **语气风格**：语言要温馨、亲切、充满爱意，像慈母般的关怀，让学生感受到老师的温暖和关爱，称呼以学生名字（不带姓，如小华同学）开头。
4. **个性化**：评语必须尊重、保留且紧密结合提供的素材，体现出每个学生的独特性，严禁使用千篇一律的套话，严禁捏造素材中不存在的内容。
5. **日常行为记录缺失处理要求**：当表现素材为空时，请基于该学生的基本信息（姓名、班级等）生成一份积极正面的通用评语，重点关注学习态度、成长潜力和未来期望，避免具体事例描述。

请直接生成完整的评语内容，100-150字之间。`,
      variables: [
        { name: 'studentName', type: 'string', required: true, description: '学生姓名' },
        { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料' }
      ],
      metadata: {
        author: 'system',
        description: '温暖亲切的评语模板',
        tags: ['warm', 'caring', 'default'],
        usageCount: 0
      },
      version: 1,
      enabled: true,
      source: 'hardcoded',
      cachedAt: Date.now(),
      cacheVersion: 2
    },
    
    'encouraging': {
      id: 'encouraging_default',
      name: '鼓励评语模板（默认）',
      type: 'encouraging',
      content: `你是一位拥有15年教龄、经验丰富、充满爱心且善于观察的中职班主任。你的任务是根据提供的学生日常表现素材，为学生撰写一份充满鼓励、激发潜能的学期综合评语。

学生姓名：
<student_name>
{studentName}
</student_name>

学生本学期的日常表现素材如下：
<performance_material>
{performanceMaterial}
</performance_material>

请严格遵循以下要求撰写评语：
1. **评语结构**：采用"积极开场 + 闪光点发现 + 潜能激发 + 信心鼓舞"的结构。
2. **内容要求**：
   - **闪光点发现部分**：必须从素材中挖掘2-3个最突出的优点和进步，用激励的语言放大学生的闪光点。
   - **潜能激发部分**：基于素材中的表现，鼓励学生发挥更大潜能，提出积极的期望和目标。
3. **语气风格**：语言要充满正能量、激励人心，像教练般的鼓舞，让学生充满自信和动力，称呼以学生名字（不带姓，如小华同学）开头。
4. **个性化**：评语必须尊重、保留且紧密结合提供的素材，体现出每个学生的独特性，严禁使用千篇一律的套话，严禁捏造素材中不存在的内容。
5. **日常行为记录缺失处理要求**：当表现素材为空时，请基于该学生的基本信息（姓名、班级等）生成一份积极正面的通用评语，重点关注学习态度、成长潜力和未来期望，避免具体事例描述。

请直接生成完整的评语内容，100-150字之间。`,
      variables: [
        { name: 'studentName', type: 'string', required: true, description: '学生姓名' },
        { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料' }
      ],
      metadata: {
        author: 'system',
        description: '激励性评语模板',
        tags: ['encouraging', 'motivational', 'default'],
        usageCount: 0
      },
      version: 1,
      enabled: true,
      source: 'hardcoded',
      cachedAt: Date.now(),
      cacheVersion: 2
    },
    
    'detailed': {
      id: 'detailed_default',
      name: '详细评语模板（默认）',
      type: 'detailed',
      content: `你是一位拥有15年教龄、经验丰富、充满爱心且善于观察的中职班主任。你的任务是根据提供的学生日常表现素材，为学生撰写一份详细全面、深入分析的学期综合评语。

学生姓名：
<student_name>
{studentName}
</student_name>

学生本学期的日常表现素材如下：
<performance_material>
{performanceMaterial}
</performance_material>

请严格遵循以下要求撰写评语：
1. **评语结构**：采用"全面概述 + 详细分析 + 深度建议 + 期望展望"的结构。
2. **内容要求**：
   - **详细分析部分**：必须从素材中全面分析学生的学习态度、行为表现、人际交往等多个维度，引用具体事例进行深入分析。
   - **深度建议部分**：基于素材分析，提出具体、可操作的改进建议和发展方向，帮助学生全面成长。
3. **语气风格**：语言要详实、深入、专业，体现班主任的全面观察和深度思考，称呼以学生名字（不带姓，如小华同学）开头。
4. **个性化**：评语必须尊重、保留且紧密结合提供的素材，体现出每个学生的独特性，严禁使用千篇一律的套话，严禁捏造素材中不存在的内容。
5. **日常行为记录缺失处理要求**：当表现素材为空时，请基于该学生的基本信息（姓名、班级等）生成一份积极正面的通用评语，重点关注学习态度、成长潜力和未来期望，避免具体事例描述。

请直接生成完整的评语内容，100-150字之间。`,
      variables: [
        { name: 'studentName', type: 'string', required: true, description: '学生姓名' },
        { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料' }
      ],
      metadata: {
        author: 'system',
        description: '详细全面的分析型评语模板',
        tags: ['detailed', 'comprehensive', 'default'],
        usageCount: 0
      },
      version: 1,
      enabled: true,
      source: 'hardcoded',
      cachedAt: Date.now(),
      cacheVersion: 2
    }
  };
  
  return defaultTemplates[type] || defaultTemplates['warm'];
}