import React, { useState } from 'react'
import { 
  Card, 
  Form, 
  Input, 
  Select, 
  Button, 
  Space, 
  Typography, 
  Tabs, 
  InputNumber,
  Switch,
  message,
  Table,
  Tag,
  Modal
} from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined, TestOutlined } from '@ant-design/icons'

const { Title, Text } = Typography
const { TextArea } = Input
const { TabPane } = Tabs

const AIConfig: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [testModalVisible, setTestModalVisible] = useState(false)

  // 模拟AI模型数据
  const aiModels = [
    {
      id: '1',
      name: '豆包模型',
      provider: 'bytedance',
      model: 'doubao-pro-32k',
      status: 'active',
      apiKey: 'bce-v3-xxx',
      usage: 1234,
      cost: 45.67
    },
    {
      id: '2', 
      name: 'GPT-4',
      provider: 'openai',
      model: 'gpt-4-turbo',
      status: 'inactive',
      apiKey: 'sk-xxx',
      usage: 0,
      cost: 0
    }
  ]

  // 模拟提示词模板数据
  const promptTemplates = [
    {
      id: '1',
      name: '温暖鼓励型',
      type: 'encourage',
      content: '基于以下学生信息，请生成一条温暖鼓励的评语...',
      status: 'active',
      usage: 567
    },
    {
      id: '2',
      name: '客观分析型', 
      type: 'analysis',
      content: '基于以下学生表现，请生成一条客观分析的评语...',
      status: 'active',
      usage: 423
    }
  ]

  const onFinish = async (values: any) => {
    setLoading(true)
    try {
      console.log('AI配置:', values)
      message.success('AI配置保存成功！')
    } catch (error) {
      message.error('保存失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const handleTestModel = () => {
    setTestModalVisible(true)
  }

  const modelColumns = [
    {
      title: '模型名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '提供商',
      dataIndex: 'provider',
      key: 'provider',
      render: (provider: string) => (
        <Tag color={provider === 'bytedance' ? 'blue' : 'green'}>
          {provider === 'bytedance' ? '字节跳动' : 'OpenAI'}
        </Tag>
      )
    },
    {
      title: '模型版本',
      dataIndex: 'model',
      key: 'model'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'success' : 'default'}>
          {status === 'active' ? '启用' : '停用'}
        </Tag>
      )
    },
    {
      title: '使用次数',
      dataIndex: 'usage',
      key: 'usage'
    },
    {
      title: '费用(元)',
      dataIndex: 'cost',
      key: 'cost',
      render: (cost: number) => `¥${cost.toFixed(2)}`
    },
    {
      title: '操作',
      key: 'action',
      render: () => (
        <Space>
          <Button type="text" icon={<EditOutlined />} size="small">
            编辑
          </Button>
          <Button type="text" icon={<TestOutlined />} size="small" onClick={handleTestModel}>
            测试
          </Button>
          <Button type="text" icon={<DeleteOutlined />} size="small" danger>
            删除
          </Button>
        </Space>
      )
    }
  ]

  const templateColumns = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const typeMap: Record<string, { text: string; color: string }> = {
          encourage: { text: '鼓励型', color: 'green' },
          analysis: { text: '分析型', color: 'blue' },
          suggestion: { text: '建议型', color: 'orange' },
          comprehensive: { text: '综合型', color: 'purple' }
        }
        const typeInfo = typeMap[type] || { text: type, color: 'default' }
        return <Tag color={typeInfo.color}>{typeInfo.text}</Tag>
      }
    },
    {
      title: '使用次数',
      dataIndex: 'usage',
      key: 'usage'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'success' : 'default'}>
          {status === 'active' ? '启用' : '停用'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: () => (
        <Space>
          <Button type="text" icon={<EditOutlined />} size="small">
            编辑
          </Button>
          <Button type="text" icon={<DeleteOutlined />} size="small" danger>
            删除
          </Button>
        </Space>
      )
    }
  ]

  return (
    <div>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <div>
          <Title level={2} style={{ marginBottom: 8 }}>
            AI配置管理
          </Title>
          <Text type="secondary">
            管理AI模型和提示词模板配置
          </Text>
        </div>

        <Tabs defaultActiveKey="models" type="card">
          <TabPane tab="AI模型配置" key="models">
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text strong>当前AI模型</Text>
                <Button type="primary" icon={<PlusOutlined />}>
                  添加模型
                </Button>
              </div>
              
              <Table 
                columns={modelColumns} 
                dataSource={aiModels}
                rowKey="id"
                pagination={false}
              />
            </Space>
          </TabPane>

          <TabPane tab="提示词模板" key="templates">
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text strong>提示词模板</Text>
                <Button type="primary" icon={<PlusOutlined />}>
                  添加模板
                </Button>
              </div>
              
              <Table 
                columns={templateColumns} 
                dataSource={promptTemplates}
                rowKey="id"
                pagination={false}
              />
            </Space>
          </TabPane>

          <TabPane tab="系统参数" key="settings">
            <Card title="AI系统参数配置">
              <Form
                form={form}
                layout="vertical"
                onFinish={onFinish}
                initialValues={{
                  temperature: 0.7,
                  maxTokens: 2000,
                  topP: 0.9,
                  frequencyPenalty: 0,
                  presencePenalty: 0,
                  enableStream: true,
                  enableCache: true,
                  timeout: 30
                }}
              >
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: 16 }}>
                  <Form.Item
                    label="温度参数"
                    name="temperature"
                    tooltip="控制生成文本的随机性，值越高越随机"
                  >
                    <InputNumber min={0} max={2} step={0.1} style={{ width: '100%' }} />
                  </Form.Item>

                  <Form.Item
                    label="最大Token数"
                    name="maxTokens"
                    tooltip="生成文本的最大长度"
                  >
                    <InputNumber min={100} max={4000} style={{ width: '100%' }} />
                  </Form.Item>

                  <Form.Item
                    label="Top P"
                    name="topP"
                    tooltip="核采样参数，控制文本多样性"
                  >
                    <InputNumber min={0} max={1} step={0.1} style={{ width: '100%' }} />
                  </Form.Item>

                  <Form.Item
                    label="频率惩罚"
                    name="frequencyPenalty"
                    tooltip="降低重复内容的概率"
                  >
                    <InputNumber min={-2} max={2} step={0.1} style={{ width: '100%' }} />
                  </Form.Item>

                  <Form.Item
                    label="存在惩罚"
                    name="presencePenalty"
                    tooltip="鼓励生成新内容"
                  >
                    <InputNumber min={-2} max={2} step={0.1} style={{ width: '100%' }} />
                  </Form.Item>

                  <Form.Item
                    label="超时时间(秒)"
                    name="timeout"
                    tooltip="API请求超时时间"
                  >
                    <InputNumber min={5} max={120} style={{ width: '100%' }} />
                  </Form.Item>
                </div>

                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 16, marginTop: 16 }}>
                  <Form.Item
                    label="启用流式输出"
                    name="enableStream"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="启用缓存"
                    name="enableCache"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </div>

                <Form.Item style={{ marginTop: 24 }}>
                  <Space>
                    <Button type="primary" htmlType="submit" loading={loading}>
                      保存配置
                    </Button>
                    <Button onClick={() => form.resetFields()}>
                      重置
                    </Button>
                    <Button onClick={handleTestModel}>
                      测试连接
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </Card>
          </TabPane>
        </Tabs>
      </Space>

      {/* 测试模型对话框 */}
      <Modal
        title="测试AI模型"
        open={testModalVisible}
        onCancel={() => setTestModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setTestModalVisible(false)}>
            取消
          </Button>,
          <Button key="test" type="primary">
            开始测试
          </Button>
        ]}
      >
        <Form layout="vertical">
          <Form.Item label="测试输入">
            <TextArea 
              rows={4} 
              placeholder="请输入测试文本，用于验证AI模型响应..."
              defaultValue="请为一个活泼好动、数学成绩优秀但语文需要加强的小学生写一条评语。"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default AIConfig