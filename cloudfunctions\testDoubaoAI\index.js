/**
 * 测试豆包AI云函数
 * 用于开发和测试环境的AI调用
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  console.log('testDoubaoAI 云函数被调用:', event);
  
  try {
    const { prompt, model, temperature, max_tokens } = event;

    // 模拟AI响应延迟
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    // 生成模拟的AI评语内容
    const mockContent = generateMockComment(prompt);

    return {
      success: true,
      data: {
        content: mockContent
      },
      message: '测试AI生成成功',
      isFallback: true,
      isTest: true
    };

  } catch (error) {
    console.error('testDoubaoAI 调用失败:', error);
    
    return {
      success: false,
      error: error.message,
      message: '测试AI调用失败'
    };
  }
};

/**
 * 生成模拟评语内容
 */
function generateMockComment(prompt) {
  const templates = [
    "该同学在本学期表现良好，学习态度端正，能够认真完成各项学习任务。建议继续保持积极的学习状态，在专业技能方面加强实践操作。",
    "这位同学很用心，学习很努力，老师看在眼里很欣慰。希望能更加自信一些，相信自己一定能取得更好的成绩。",
    "同学的潜力是无限的！看到你在专业技能上的进步，老师为你感到骄傲。继续努力，未来属于你！",
    "在本学期的综合表现值得肯定：学习态度端正，专业技能掌握较好，与同学关系融洽。建议在理论学习方面加强深度思考。"
  ];

  // 根据提示词长度和内容选择合适的模板
  const randomIndex = Math.floor(Math.random() * templates.length);
  let selectedTemplate = templates[randomIndex];

  // 如果提示词中包含学生姓名，尝试提取并替换
  const nameMatch = prompt.match(/学生姓名[：:]\s*([^\s，,。.]+)/);
  if (nameMatch && nameMatch[1]) {
    selectedTemplate = selectedTemplate.replace(/该同学|这位同学|同学/g, nameMatch[1]);
  }

  return selectedTemplate;
}
