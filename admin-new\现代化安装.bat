@echo off
echo =============================================
echo   现代化依赖安装 - 兼容Node.js 22
echo =============================================
echo.

echo 🚀 使用现代化版本，不降级Node.js！
echo Node.js版本: %node --version%
echo.

echo 步骤1: 清理旧依赖
if exist "node_modules" (
    echo 删除 node_modules...
    rmdir /s /q node_modules
)

if exist "package-lock.json" (
    echo 删除 package-lock.json...
    del package-lock.json
)

echo.
echo 步骤2: 清理npm缓存
npm cache clean --force

echo.
echo 步骤3: 安装现代化版本的依赖
echo 使用npm install（现代版本包都支持Node.js 22）
npm install

if errorlevel 1 (
    echo.
    echo ❌ npm安装失败，尝试强制安装...
    npm install --force
    
    if errorlevel 1 (
        echo.
        echo ❌ 还是失败，尝试跳过peer deps检查...
        npm install --legacy-peer-deps
    )
)

echo.
echo 步骤4: 验证关键依赖
if exist "node_modules\vite" (
    echo ✅ Vite 5.x 已安装
) else (
    echo ❌ Vite未安装
)

if exist "node_modules\rollup" (
    echo ✅ Rollup 4.x 已安装  
) else (
    echo ❌ Rollup未安装
)

echo.
echo 步骤5: 启动开发服务器
echo ✅ 现代化依赖安装完成！启动服务器...
npm run dev

pause