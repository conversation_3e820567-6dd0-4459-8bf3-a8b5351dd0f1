@echo off
echo =============================================
echo   终极修复方案 - 强制解决所有问题
echo =============================================
echo.

echo 🚨 使用终极修复方案...
echo.

echo 步骤1: 完全清理
rmdir /s /q node_modules 2>nul
del package-lock.json 2>nul
del yarn.lock 2>nul

echo 步骤2: 设置npm配置
npm config set registry https://registry.npmjs.org/
npm config set optional false
npm config set legacy-peer-deps true

echo 步骤3: 清理缓存
npm cache clean --force

echo 步骤4: 使用yarn安装（更稳定）
echo 安装yarn...
npm install -g yarn --force

echo 使用yarn安装依赖...
yarn install

echo 步骤5: 如果yarn失败，回退到npm
if errorlevel 1 (
    echo yarn失败，使用npm...
    npm install --force --legacy-peer-deps --no-optional
)

echo 步骤6: 验证关键文件
if exist "node_modules\vite" (
    echo ✅ Vite已安装
) else (
    echo ❌ Vite未安装，手动安装...
    npm install vite@latest --save-dev --force
)

if exist "node_modules\rollup" (
    echo ✅ Rollup已安装
) else (
    echo ❌ Rollup未安装，手动安装...
    npm install rollup@latest --save-dev --force
)

echo 步骤7: 启动开发服务器
echo ✅ 修复完成！启动开发服务器...
npm run dev

pause