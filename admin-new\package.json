{"name": "pingyu-admin-new", "version": "2.0.0", "description": "评语灵感君管理后台 - 现代化重构版", "private": true, "type": "module", "dependencies": {"@ant-design/icons": "^5.2.6", "antd": "^5.12.8", "axios": "^1.6.5", "dayjs": "^1.11.10", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "zustand": "^4.4.7", "swr": "^2.2.4", "echarts": "^5.4.3", "react-window": "^1.8.8", "react-virtualized-auto-sizer": "^1.0.20", "lodash-es": "^4.17.21", "classnames": "^2.3.2"}, "devDependencies": {"@rollup/rollup-win32-x64-msvc": "^4.46.1", "@types/node": "^22.0.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/react-window": "^1.8.8", "@types/lodash-es": "^4.17.12", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "rollup": "^4.46.1", "tailwindcss": "^3.4.0", "typescript": "^5.6.3", "vite": "^5.4.8", "vite-bundle-analyzer": "^0.7.0", "@welldone-software/why-did-you-render": "^8.0.1"}, "scripts": {"dev": "vite --port 3000 --force", "dev-safe": "vite --port 3000 --force --no-open", "build": "vite build", "build:analyze": "npm run build && npx vite-bundle-analyzer", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "typecheck": "tsc --noEmit"}, "engines": {"node": ">=18.0.0"}}