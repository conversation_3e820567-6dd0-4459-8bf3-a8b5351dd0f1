/**
 * 实时数据同步管理器 - 增强版
 * 解决前后端数据连通性问题，支持离线缓存和冲突解决
 */

class RealTimeSyncManager {
  constructor() {
    this.syncQueue = [];
    this.isOnline = true;
    this.conflictResolver = new ConflictResolver();
    this.retryAttempts = new Map(); // 记录重试次数
    this.maxRetries = 3;
    this.syncInProgress = false;
    
    this.init();
  }

  /**
   * 初始化同步管理器
   */
  init() {
    console.log('[RealTimeSync] 初始化实时数据同步管理器');
    
    // 监听网络状态变化
    this.setupNetworkMonitoring();
    
    // 启动心跳检测
    this.startHeartbeat();
    
    // 监听小程序生命周期
    this.setupAppLifecycleMonitoring();
    
    // 启动定期同步
    this.startPeriodicSync();
  }

  /**
   * 网络状态监控
   */
  setupNetworkMonitoring() {
    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      const wasOnline = this.isOnline;
      this.isOnline = res.isConnected;
      
      console.log(`[RealTimeSync] 网络状态变化: ${wasOnline ? '在线' : '离线'} -> ${this.isOnline ? '在线' : '离线'}`);
      
      if (!wasOnline && this.isOnline) {
        // 从离线恢复到在线，立即同步
        console.log('[RealTimeSync] 网络恢复，开始同步离线数据');
        this.syncOfflineData();
      }
    });

    // 获取初始网络状态
    wx.getNetworkType({
      success: (res) => {
        this.isOnline = res.networkType !== 'none';
        console.log(`[RealTimeSync] 初始网络状态: ${this.isOnline ? '在线' : '离线'}`);
      }
    });
  }

  /**
   * 心跳检测
   */
  startHeartbeat() {
    setInterval(async () => {
      try {
        await this.pingServer();
      } catch (error) {
        console.warn('[RealTimeSync] 心跳检测失败:', error);
        this.isOnline = false;
      }
    }, 30000); // 30秒检测一次
  }

  /**
   * 服务器连通性检测
   */
  async pingServer() {
    try {
      const app = getApp();
      const cloudService = app.globalData.cloudService;
      
      if (!cloudService) {
        throw new Error('云服务不可用');
      }
      
      // 执行简单的连接测试
      await cloudService.testConnection();
      
      if (!this.isOnline) {
        console.log('[RealTimeSync] 服务器连接恢复');
        this.isOnline = true;
        this.syncOfflineData();
      }
      
      return true;
    } catch (error) {
      this.isOnline = false;
      throw error;
    }
  }

  /**
   * 应用生命周期监控
   */
  setupAppLifecycleMonitoring() {
    // 监听小程序显示事件
    wx.onAppShow(() => {
      console.log('[RealTimeSync] 应用显示，检查数据同步');
      this.syncAllData();
    });

    // 监听小程序隐藏事件
    wx.onAppHide(() => {
      console.log('[RealTimeSync] 应用隐藏，保存待同步数据');
      this.saveQueueToStorage();
    });
  }

  /**
   * 定期同步
   */
  startPeriodicSync() {
    // 每5分钟执行一次全量同步检查
    setInterval(() => {
      if (this.isOnline && !this.syncInProgress) {
        this.syncAllData();
      }
    }, 5 * 60 * 1000);
  }

  /**
   * 数据同步核心方法
   */
  async syncData(collection, data, operation, options = {}) {
    const syncItem = {
      id: this.generateSyncId(),
      collection,
      data,
      operation, // 'create', 'update', 'delete'
      timestamp: Date.now(),
      retryCount: 0,
      priority: options.priority || 'normal', // 'high', 'normal', 'low'
      ...options
    };

    console.log(`[RealTimeSync] 准备同步数据: ${collection}.${operation}`, syncItem.id);

    if (this.isOnline && !options.forceQueue) {
      try {
        // 在线时直接同步
        await this.executeSyncItem(syncItem);
        console.log(`[RealTimeSync] 实时同步成功: ${syncItem.id}`);
        return { success: true, syncId: syncItem.id };
      } catch (error) {
        console.warn(`[RealTimeSync] 实时同步失败，加入队列: ${syncItem.id}`, error);
        this.addToQueue(syncItem);
        await this.fallbackToLocal(syncItem);
        return { success: false, syncId: syncItem.id, queued: true };
      }
    } else {
      // 离线时加入队列并本地处理
      console.log(`[RealTimeSync] 离线模式，加入同步队列: ${syncItem.id}`);
      this.addToQueue(syncItem);
      await this.fallbackToLocal(syncItem);
      return { success: true, syncId: syncItem.id, offline: true };
    }
  }

  /**
   * 执行单个同步项
   */
  async executeSyncItem(syncItem) {
    const { collection, data, operation } = syncItem;
    const app = getApp();
    const cloudService = app.globalData.cloudService;

    if (!cloudService) {
      throw new Error('云服务不可用');
    }

    let result;
    switch (operation) {
      case 'create':
        if (collection === 'students') {
          result = await cloudService.addStudent(data);
        } else if (collection === 'records') {
          result = await cloudService.createRecord(data);
        } else if (collection === 'comments') {
          result = await cloudService.saveComment(data);
        } else if (collection === 'classes') {
          result = await cloudService.createClass(data);
        }
        break;

      case 'update':
        if (collection === 'students') {
          result = await cloudService.updateStudent(data._id, data);
        } else if (collection === 'records') {
          result = await cloudService.updateRecord(data._id, data);
        } else if (collection === 'comments') {
          result = await cloudService.updateComment(data._id, data);
        } else if (collection === 'classes') {
          result = await cloudService.updateClass(data._id, data);
        }
        break;

      case 'delete':
        if (collection === 'students') {
          result = await cloudService.deleteStudent(data._id);
        } else if (collection === 'records') {
          result = await cloudService.deleteRecord(data._id);
        } else if (collection === 'comments') {
          result = await cloudService.deleteComment(data._id);
        } else if (collection === 'classes') {
          result = await cloudService.deleteClass(data._id);
        }
        break;

      default:
        throw new Error(`不支持的操作类型: ${operation}`);
    }

    if (!result || !result.success) {
      throw new Error(result?.error || '云端操作失败');
    }

    return result;
  }

  /**
   * 本地降级处理
   */
  async fallbackToLocal(syncItem) {
    const { collection, data, operation } = syncItem;
    
    try {
      // 获取本地数据
      const localKey = `local_${collection}`;
      let localData = wx.getStorageSync(localKey) || [];

      switch (operation) {
        case 'create':
          // 添加临时ID标识
          const newItem = {
            ...data,
            _tempId: syncItem.id,
            _localOnly: true,
            _createTime: new Date().toISOString()
          };
          localData.push(newItem);
          break;

        case 'update':
          const updateIndex = localData.findIndex(item => 
            item._id === data._id || item._tempId === data._tempId
          );
          if (updateIndex !== -1) {
            localData[updateIndex] = {
              ...localData[updateIndex],
              ...data,
              _modified: true,
              _updateTime: new Date().toISOString()
            };
          }
          break;

        case 'delete':
          localData = localData.filter(item => 
            item._id !== data._id && item._tempId !== data._tempId
          );
          break;
      }

      // 保存到本地存储
      wx.setStorageSync(localKey, localData);
      console.log(`[RealTimeSync] 本地降级处理完成: ${collection}.${operation}`);

    } catch (error) {
      console.error('[RealTimeSync] 本地降级处理失败:', error);
    }
  }

  /**
   * 同步离线数据
   */
  async syncOfflineData() {
    if (this.syncInProgress) {
      console.log('[RealTimeSync] 同步正在进行中，跳过');
      return;
    }

    this.syncInProgress = true;
    console.log('[RealTimeSync] 开始同步离线数据');

    try {
      // 从存储中恢复队列
      this.loadQueueFromStorage();

      // 按优先级排序
      this.syncQueue.sort((a, b) => {
        const priorityOrder = { high: 3, normal: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      });

      const totalItems = this.syncQueue.length;
      let successCount = 0;
      let failCount = 0;

      console.log(`[RealTimeSync] 待同步项目数量: ${totalItems}`);

      // 逐个处理同步队列
      for (let i = 0; i < this.syncQueue.length; i++) {
        const syncItem = this.syncQueue[i];
        
        try {
          await this.executeSyncItem(syncItem);
          
          // 同步成功，从队列中移除
          this.syncQueue.splice(i, 1);
          i--; // 调整索引
          successCount++;
          
          // 清理本地临时数据
          this.cleanupLocalTempData(syncItem);
          
          console.log(`[RealTimeSync] 同步成功: ${syncItem.id} (${successCount}/${totalItems})`);

        } catch (error) {
          console.error(`[RealTimeSync] 同步失败: ${syncItem.id}`, error);
          
          // 增加重试次数
          syncItem.retryCount = (syncItem.retryCount || 0) + 1;
          
          if (syncItem.retryCount >= this.maxRetries) {
            // 超过最大重试次数，移除或标记为失败
            console.error(`[RealTimeSync] 超过最大重试次数，放弃同步: ${syncItem.id}`);
            this.syncQueue.splice(i, 1);
            i--;
            failCount++;
          } else {
            // 延迟重试
            syncItem.nextRetryTime = Date.now() + (syncItem.retryCount * 5000);
          }
        }

        // 避免过快的连续请求
        await this.delay(200);
      }

      console.log(`[RealTimeSync] 离线数据同步完成: 成功${successCount}个，失败${failCount}个`);

      // 保存更新后的队列
      this.saveQueueToStorage();

    } catch (error) {
      console.error('[RealTimeSync] 离线数据同步异常:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * 全量数据同步
   */
  async syncAllData() {
    if (!this.isOnline) {
      console.log('[RealTimeSync] 离线状态，跳过全量同步');
      return;
    }

    try {
      console.log('[RealTimeSync] 开始全量数据同步检查');
      
      // 检查各个数据集合的同步状态
      await this.syncCollection('students');
      await this.syncCollection('records');
      await this.syncCollection('comments');
      await this.syncCollection('classes');
      
      console.log('[RealTimeSync] 全量数据同步检查完成');

    } catch (error) {
      console.error('[RealTimeSync] 全量数据同步失败:', error);
    }
  }

  /**
   * 同步特定集合
   */
  async syncCollection(collection) {
    try {
      const app = getApp();
      const cloudService = app.globalData.cloudService;
      
      if (!cloudService) {
        console.warn(`[RealTimeSync] 云服务不可用，跳过${collection}同步`);
        return;
      }

      // 获取云端数据
      let cloudData = [];
      try {
        switch (collection) {
          case 'students':
            const studentsResult = await cloudService.getStudentList();
            cloudData = (studentsResult && studentsResult.success && Array.isArray(studentsResult.data))
              ? studentsResult.data : [];
            break;
          case 'records':
            const recordsResult = await cloudService.getRecordList();
            cloudData = (recordsResult && recordsResult.success && Array.isArray(recordsResult.data))
              ? recordsResult.data : [];
            break;
          case 'comments':
            const commentsResult = await cloudService.getCommentList();
            cloudData = (commentsResult && commentsResult.success && Array.isArray(commentsResult.data))
              ? commentsResult.data : [];
            break;
          case 'classes':
            const classesResult = await cloudService.getClassList();
            cloudData = (classesResult && classesResult.success && Array.isArray(classesResult.data))
              ? classesResult.data : [];
            break;
          default:
            console.warn(`[RealTimeSync] 未知的集合类型: ${collection}`);
            cloudData = [];
        }
      } catch (error) {
        console.error(`[RealTimeSync] 获取${collection}云端数据失败:`, error);
        cloudData = [];
      }

      // 获取本地数据
      const localKey = `local_${collection}`;
      const localData = wx.getStorageSync(localKey) || [];

      console.log(`[RealTimeSync] ${collection}数据统计:`, {
        cloudCount: cloudData.length,
        localCount: localData.length,
        cloudDataType: typeof cloudData,
        localDataType: typeof localData,
        isCloudArray: Array.isArray(cloudData),
        isLocalArray: Array.isArray(localData)
      });

      // 检查数据一致性
      const conflicts = this.detectConflicts(localData, cloudData);
      
      if (conflicts.length > 0) {
        console.log(`[RealTimeSync] 检测到${collection}数据冲突:`, conflicts.length);
        await this.resolveConflicts(collection, conflicts);
      }

      // 更新本地数据为云端最新数据
      wx.setStorageSync(localKey, cloudData);
      
      console.log(`[RealTimeSync] ${collection}数据同步完成，本地数据已更新`);

    } catch (error) {
      console.error(`[RealTimeSync] ${collection}同步失败:`, error);
    }
  }

  /**
   * 检测数据冲突
   */
  detectConflicts(localData, cloudData) {
    const conflicts = [];

    // 确保cloudData是数组
    if (!Array.isArray(cloudData)) {
      console.warn('[RealTimeSync] cloudData不是数组类型:', typeof cloudData, cloudData);
      cloudData = [];
    }

    // 确保localData是数组
    if (!Array.isArray(localData)) {
      console.warn('[RealTimeSync] localData不是数组类型:', typeof localData, localData);
      localData = [];
    }

    // 创建云端数据的映射
    const cloudMap = new Map();
    cloudData.forEach(item => {
      if (item && item._id) {
        cloudMap.set(item._id, item);
      }
    });

    // 检查本地数据中的冲突
    localData.forEach(localItem => {
      if (localItem._localOnly) {
        // 本地新增的数据，需要上传
        conflicts.push({
          type: 'local_new',
          localItem,
          cloudItem: null
        });
      } else if (localItem._modified) {
        // 本地修改的数据
        const cloudItem = cloudMap.get(localItem._id);
        if (cloudItem) {
          // 检查时间戳判断是否有冲突
          const localTime = new Date(localItem._updateTime || localItem._createTime);
          const cloudTime = new Date(cloudItem.updateTime || cloudItem.createTime);
          
          if (localTime > cloudTime) {
            conflicts.push({
              type: 'local_newer',
              localItem,
              cloudItem
            });
          } else if (cloudTime > localTime) {
            conflicts.push({
              type: 'cloud_newer',
              localItem,
              cloudItem
            });
          }
        } else {
          // 本地有但云端没有，可能被删除
          conflicts.push({
            type: 'local_orphan',
            localItem,
            cloudItem: null
          });
        }
      }
    });

    return conflicts;
  }

  /**
   * 解决数据冲突
   */
  async resolveConflicts(collection, conflicts) {
    for (const conflict of conflicts) {
      try {
        await this.conflictResolver.resolve(collection, conflict);
      } catch (error) {
        console.error(`[RealTimeSync] 冲突解决失败:`, conflict, error);
      }
    }
  }

  /**
   * 工具方法
   */
  generateSyncId() {
    return `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  addToQueue(syncItem) {
    this.syncQueue.push(syncItem);
    this.saveQueueToStorage();
  }

  saveQueueToStorage() {
    try {
      wx.setStorageSync('sync_queue', this.syncQueue);
    } catch (error) {
      console.error('[RealTimeSync] 保存同步队列失败:', error);
    }
  }

  loadQueueFromStorage() {
    try {
      const savedQueue = wx.getStorageSync('sync_queue') || [];
      this.syncQueue = savedQueue.filter(item => {
        // 过滤掉过期的同步项（超过24小时）
        return Date.now() - item.timestamp < 24 * 60 * 60 * 1000;
      });
    } catch (error) {
      console.error('[RealTimeSync] 加载同步队列失败:', error);
      this.syncQueue = [];
    }
  }

  cleanupLocalTempData(syncItem) {
    const { collection } = syncItem;
    const localKey = `local_${collection}`;
    
    try {
      let localData = wx.getStorageSync(localKey) || [];
      localData = localData.filter(item => item._tempId !== syncItem.id);
      wx.setStorageSync(localKey, localData);
    } catch (error) {
      console.error('[RealTimeSync] 清理本地临时数据失败:', error);
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 冲突解决器
 */
class ConflictResolver {
  async resolve(collection, conflict) {
    const { type, localItem, cloudItem } = conflict;
    
    console.log(`[ConflictResolver] 解决冲突: ${collection}.${type}`);

    switch (type) {
      case 'local_new':
        // 本地新增数据，上传到云端
        await this.uploadLocalItem(collection, localItem);
        break;
        
      case 'local_newer':
        // 本地数据更新，上传到云端
        await this.uploadLocalItem(collection, localItem);
        break;
        
      case 'cloud_newer':
        // 云端数据更新，使用云端数据
        console.log(`[ConflictResolver] 使用云端较新数据: ${cloudItem._id}`);
        break;
        
      case 'local_orphan':
        // 本地孤立数据，询问用户或自动处理
        await this.handleOrphanItem(collection, localItem);
        break;
        
      default:
        console.warn(`[ConflictResolver] 未知冲突类型: ${type}`);
    }
  }

  async uploadLocalItem(collection, localItem) {
    try {
      const app = getApp();
      const cloudService = app.globalData.cloudService;
      
      // 清理本地标识字段
      const cleanItem = { ...localItem };
      delete cleanItem._tempId;
      delete cleanItem._localOnly;
      delete cleanItem._modified;
      delete cleanItem._createTime;
      delete cleanItem._updateTime;

      let result;
      if (localItem._localOnly) {
        // 新增数据
        result = await this.createCloudItem(collection, cleanItem, cloudService);
      } else {
        // 更新数据
        result = await this.updateCloudItem(collection, cleanItem, cloudService);
      }

      if (result && result.success) {
        console.log(`[ConflictResolver] 上传成功: ${collection}`);
      } else {
        throw new Error(result?.error || '上传失败');
      }

    } catch (error) {
      console.error(`[ConflictResolver] 上传失败:`, error);
      throw error;
    }
  }

  async createCloudItem(collection, item, cloudService) {
    switch (collection) {
      case 'students':
        return await cloudService.addStudent(item);
      case 'records':
        return await cloudService.createRecord(item);
      case 'comments':
        return await cloudService.saveComment(item);
      case 'classes':
        return await cloudService.createClass(item);
      default:
        throw new Error(`不支持的集合类型: ${collection}`);
    }
  }

  async updateCloudItem(collection, item, cloudService) {
    switch (collection) {
      case 'students':
        return await cloudService.updateStudent(item._id, item);
      case 'records':
        return await cloudService.updateRecord(item._id, item);
      case 'comments':
        return await cloudService.updateComment(item._id, item);
      case 'classes':
        return await cloudService.updateClass(item._id, item);
      default:
        throw new Error(`不支持的集合类型: ${collection}`);
    }
  }

  async handleOrphanItem(collection, localItem) {
    // 对于孤立数据，可以选择重新上传或删除
    // 这里选择重新上传的策略
    console.log(`[ConflictResolver] 处理孤立数据，重新上传: ${localItem._id}`);
    
    try {
      // 移除ID，作为新数据上传
      const newItem = { ...localItem };
      delete newItem._id;
      
      await this.uploadLocalItem(collection, {
        ...newItem,
        _localOnly: true
      });
    } catch (error) {
      console.error(`[ConflictResolver] 孤立数据处理失败:`, error);
    }
  }
}

// 创建全局实例
const realTimeSyncManager = new RealTimeSyncManager();

module.exports = {
  realTimeSyncManager,
  RealTimeSyncManager,
  ConflictResolver
};